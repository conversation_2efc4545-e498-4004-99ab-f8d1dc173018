import torch.nn as nn
from torchvision import models

# convnet without the last layer
class AlexNet(nn.Module):
    def __init__(self, num_classes):
        super(AlexNet, self).__init__()
        model_alexnet = models.alexnet(pretrained=False)
        self.features = model_alexnet.features
        self.avgpool = model_alexnet.avgpool
        self.classifier = nn.Sequential()
        for i in range(6):
            self.classifier.add_module(
                "classifier"+str(i), model_alexnet.classifier[i])
        self._feature_dim = model_alexnet.classifier[6].in_features
        self.classifier.add_module("classifier7",nn.Linear(self._feature_dim, num_classes))

    def forward(self, x):
        x = self.features(x)
        x = self.avgpool(x)
        x = x.view(x.size(0), 256*6*6)
        x = self.classifier(x)
        return x

    def output_num(self):
        return self._feature_dim