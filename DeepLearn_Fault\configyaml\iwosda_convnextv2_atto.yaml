# Importance Weighted Open Set Domain Adaptation (IWOSDA) Configuration - Using ConvNeXtV2 Atto

# Model settings
model_name: iwosda_convnextv2_atto

# Feature extractor pretrained settings (moved to top level)
pretrained: true
pretrained_path: checkpoint/convnextv2_atto/convnextv2_atto_1k_224_ema.pt


# Training settings
batch_size: 16
n_epoch: 30
early_stop: 30

# Optimizer settings
lr: 0.00005

momentum: 0.9
weight_decay: 1e-5

# Learning rate scheduler settings
lr_gamma: 0.0003
lr_decay: 0.75
lr_scheduler: true
num_workers: 12
# Confusion matrix usage
confmtx: 1

# t-SNE visualization
t_sne: 1

# Transfer learning settings (moved to top level)
transfer_loss_weight: 1.0
domain_adaptation_weight: 1.0
weight_regularization: 0.1
unknown_threshold: 0.5
source_weight: 1.0
target_weight: 1.0
