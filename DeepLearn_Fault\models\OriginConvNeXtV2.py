"""
原始 ConvNeXt V2 模型实现
基于 https://github.com/facebookresearch/ConvNeXt-V2 的官方实现
本文件包含原始的 ConvNeXt V2 atto 模型，不包含任何修改
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from timm.models.layers import trunc_normal_, DropPath
import os

class LayerNorm(nn.Module):
    """ LayerNorm 支持两种数据格式：channels_last（默认）或 channels_first
    输入的维度排列。channels_last 对应形状为 (batch_size, height, width, channels) 的输入，
    而 channels_first 对应形状为 (batch_size, channels, height, width) 的输入。
    """
    def __init__(self, normalized_shape, eps=1e-6, data_format="channels_last"):
        super().__init__()
        self.weight = nn.Parameter(torch.ones(normalized_shape))
        self.bias = nn.Parameter(torch.zeros(normalized_shape))
        self.eps = eps
        self.data_format = data_format
        if self.data_format not in ["channels_last", "channels_first"]:
            raise NotImplementedError
        self.normalized_shape = (normalized_shape, )

    def forward(self, x):
        if self.data_format == "channels_last":
            return F.layer_norm(x, self.normalized_shape, self.weight, self.bias, self.eps)
        elif self.data_format == "channels_first":
            u = x.mean(1, keepdim=True)
            s = (x - u).pow(2).mean(1, keepdim=True)
            x = (x - u) / torch.sqrt(s + self.eps)
            x = self.weight[:, None, None] * x + self.bias[:, None, None]
            return x

class GRN(nn.Module):
    """ GRN (Global Response Normalization) 层
    """
    def __init__(self, dim):
        super().__init__()
        self.gamma = nn.Parameter(torch.zeros(1, 1, 1, dim))
        self.beta = nn.Parameter(torch.zeros(1, 1, 1, dim))

    def forward(self, x):
        Gx = torch.norm(x, p=2, dim=(1,2), keepdim=True)
        Nx = Gx / (Gx.mean(dim=-1, keepdim=True) + 1e-6)
        return self.gamma * (x * Nx) + self.beta + x

class Block(nn.Module):
    """ 原始 ConvNeXtV2 Block.

    Args:
        dim (int): 输入通道数
        drop_path (float): 随机深度衰减率. Default: 0.0
    """
    def __init__(self, dim, drop_path=0.):
        super().__init__()
        self.dwconv = nn.Conv2d(dim, dim, kernel_size=7, padding=3, groups=dim) # 原始深度卷积
        self.norm = LayerNorm(dim, eps=1e-6)
        self.pwconv1 = nn.Linear(dim, 4 * dim) # 点卷积/1x1 卷积，使用线性层实现
        self.act = nn.GELU()
        self.grn = GRN(4 * dim)
        self.pwconv2 = nn.Linear(4 * dim, dim)
        self.drop_path = DropPath(drop_path) if drop_path > 0. else nn.Identity()

    def forward(self, x):
        input = x
        x = self.dwconv(x)
        x = x.permute(0, 2, 3, 1) # (N, C, H, W) -> (N, H, W, C)
        x = self.norm(x)
        x = self.pwconv1(x)
        x = self.act(x)
        x = self.grn(x)
        x = self.pwconv2(x)
        x = x.permute(0, 3, 1, 2) # (N, H, W, C) -> (N, C, H, W)

        x = input + self.drop_path(x)
        return x

class OriginConvNeXtV2(nn.Module):
    """ 原始 ConvNeXt V2

    Args:
        in_chans (int): 输入图像通道数. Default: 3
        num_classes (int): 分类头的类别数. Default: 1000
        depths (tuple(int)): 每个阶段的块数. Default: [3, 3, 9, 3]
        dims (int): 每个阶段的特征维度. Default: [96, 192, 384, 768]
        drop_path_rate (float): 随机深度衰减率. Default: 0.
        head_init_scale (float): 分类器权重和偏置的初始缩放值. Default: 1.
    """
    def __init__(self, in_chans=3, num_classes=1000,
                 depths=[3, 3, 9, 3], dims=[96, 192, 384, 768],
                 drop_path_rate=0., head_init_scale=1.
                 ):
        super().__init__()
        self.depths = depths
        self.downsample_layers = nn.ModuleList() # stem 和 3 个中间下采样卷积层
        stem = nn.Sequential(
            nn.Conv2d(in_chans, dims[0], kernel_size=4, stride=4),
            LayerNorm(dims[0], eps=1e-6, data_format="channels_first")
        )
        self.downsample_layers.append(stem)
        for i in range(3):
            downsample_layer = nn.Sequential(
                    LayerNorm(dims[i], eps=1e-6, data_format="channels_first"),
                    nn.Conv2d(dims[i], dims[i+1], kernel_size=2, stride=2),
            )
            self.downsample_layers.append(downsample_layer)

        self.stages = nn.ModuleList() # 4 个特征分辨率阶段，每个阶段包含多个残差块
        dp_rates=[x.item() for x in torch.linspace(0, drop_path_rate, sum(depths))]
        cur = 0
        for i in range(4):
            stage = nn.Sequential(
                *[Block(dim=dims[i], drop_path=dp_rates[cur + j]) for j in range(depths[i])]
            )
            self.stages.append(stage)
            cur += depths[i]

        self.norm = nn.LayerNorm(dims[-1], eps=1e-6) # 最终归一化层
        self.head = nn.Linear(dims[-1], num_classes)

        self.apply(self._init_weights)
        self.head.weight.data.mul_(head_init_scale)
        self.head.bias.data.mul_(head_init_scale)

    def _init_weights(self, m):
        if isinstance(m, (nn.Conv2d, nn.Linear)):
            trunc_normal_(m.weight, std=.02)
            if hasattr(m, 'bias') and m.bias is not None:
                nn.init.constant_(m.bias, 0)

    def forward_features(self, x):
        for i in range(4):
            x = self.downsample_layers[i](x)
            x = self.stages[i](x)
        return self.norm(x.mean([-2, -1])) # 全局平均池化, (N, C, H, W) -> (N, C)

    def forward(self, x):
        x = self.forward_features(x)
        x = self.head(x)
        return x

def origin_convnextv2_atto(num_classes=1000, **kwargs):
    """
    构建原始 ConvNeXt V2 Atto 模型

    Args:
        num_classes (int): 分类类别数
        **kwargs: 其他参数传递给 OriginConvNeXtV2 构造函数

    Returns:
        OriginConvNeXtV2 模型实例
    """
    model = OriginConvNeXtV2(
        depths=[2, 2, 6, 2],
        dims=[40, 80, 160, 320],
        num_classes=num_classes,
        **kwargs
    )
    return model

# 创建一个工厂函数，方便从model.py调用
def Origin_ConvNeXtV2_atto(model_name, num_classes, pretrained=False, pretrained_path=None):
    """
    返回原始 ConvNeXt V2 Atto 模型实例

    Args:
        model_name: 模型名称(仅用于兼容model.py的接口)
        num_classes: 分类类别数
        pretrained: 是否使用预训练模型
        pretrained_path: 预训练模型路径

    Returns:
        原始 ConvNeXtV2 Atto 模型实例
    """
    model = origin_convnextv2_atto(num_classes=num_classes)

    # 如果没有提供预训练路径，但开启了pretrained选项，自动使用根目录的预训练模型
    if pretrained and not pretrained_path:
        # 使用原始网络的预训练模型
        pretrained_path = 'checkpoint/convnextv2_atto/convnextv2_atto_1k_224_ema.pt'
        if not os.path.exists(pretrained_path):
            pretrained_path = '../convnextv2_atto_1k_224_fcmae.pt'
        print(f"将使用原始网络的预训练模型: {pretrained_path}")

    if pretrained and pretrained_path:
        print(f"开始加载原始ConvNeXtV2 Atto预训练模型: {pretrained_path}")
        try:
            if os.path.exists(pretrained_path):
                state_dict = torch.load(pretrained_path, map_location=torch.device('cpu'))

                # 检查是否有'model'键，某些预训练模型（如fcmae格式）会将模型状态字典保存在'model'键下
                if 'model' in state_dict:
                    print("检测到预训练模型使用'model'键存储状态字典")
                    state_dict = state_dict['model']

                # 尝试加载状态字典，如果键不匹配，将进行部分加载
                model_dict = model.state_dict()

                # 处理头部不匹配的情况（预训练模型通常有1000类）
                for k in list(state_dict.keys()):
                    if 'head' in k:
                        print(f"移除预训练头部参数: {k}")
                        state_dict.pop(k)

                # 过滤掉不匹配的键
                original_keys = list(state_dict.keys())
                pretrained_dict = {k: v for k, v in state_dict.items() if k in model_dict and model_dict[k].shape == v.shape}

                # 输出未加载的键
                unloaded_keys = set(original_keys) - set(pretrained_dict.keys())
                if unloaded_keys:
                    print(f"以下键未被加载 (共{len(unloaded_keys)}个):")
                    for k in sorted(list(unloaded_keys))[:10]:  # 只打印前10个
                        print(f"  - {k}")
                    if len(unloaded_keys) > 10:
                        print(f"  ... 以及其他 {len(unloaded_keys) - 10} 个键")

                # 输出模型中缺少的键
                missing_keys = set(model_dict.keys()) - set(state_dict.keys())
                if missing_keys:
                    print(f"模型中以下键在预训练权重中不存在 (共{len(missing_keys)}个):")
                    for k in sorted(list(missing_keys))[:10]:  # 只打印前10个
                        print(f"  - {k}")
                    if len(missing_keys) > 10:
                        print(f"  ... 以及其他 {len(missing_keys) - 10} 个键")

                # 输出形状不匹配的键
                shape_mismatch_keys = {k for k in original_keys if k in model_dict and model_dict[k].shape != state_dict[k].shape}
                if shape_mismatch_keys:
                    print(f"以下键的形状不匹配 (共{len(shape_mismatch_keys)}个):")
                    for k in sorted(list(shape_mismatch_keys))[:10]:  # 只打印前10个
                        print(f"  - {k}: 预训练权重形状 {state_dict[k].shape}, 模型形状 {model_dict[k].shape}")
                    if len(shape_mismatch_keys) > 10:
                        print(f"  ... 以及其他 {len(shape_mismatch_keys) - 10} 个键")

                # 更新当前模型的字典
                model_dict.update(pretrained_dict)
                model.load_state_dict(model_dict)

                print(f"成功加载原始ConvNeXtV2 Atto预训练模型: {pretrained_path}")
                print(f"加载了 {len(pretrained_dict)}/{len(state_dict)} 层参数")
            else:
                print(f"预训练模型路径不存在: {pretrained_path}")
                print("将使用随机初始化的模型继续训练")
        except Exception as e:
            print(f"加载预训练模型失败: {e}")
            print(f"错误类型: {type(e).__name__}")
            print("将使用随机初始化的模型继续训练")

    return model
