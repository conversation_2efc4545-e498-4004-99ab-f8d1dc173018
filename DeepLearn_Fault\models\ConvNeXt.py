import torch
import torch.nn as nn
from torchvision import models
from torch import Tensor
from convnext import convnext_tiny
convnext_dict = {
    "convnext_tiny": models.convnext_tiny,
    "convnext_small": models.convnext_small,
    "convnext_base": models.convnext_base,
    "convnext_large": models.convnext_large,
}

class PConv(nn.Module):
    """Partial convolution (PConv)
    """
    def __init__(self, 
                 dim: int,
                 n_div: int = 4,
                 forward: str = 'split_cat',
                 kernel_size: int = 3):
        """ Construct a PConv layer.
        :param dim: Number of input/output channels.
        :param n_div: Peciprocal of the partial ratio. FasterNet use 4.
        :param forword: Forword type, can be either 'split_cat' or 'slicing'.
        :param kernel_size: Kernel size.
        """

        super().__init__()
        self.dim_conv = dim // n_div
        self.dim_untouched = dim - self.dim_conv
        self.conv = nn.Conv2d(self.dim_conv,
                              self.dim_conv,
                              kernel_size,
                              stride=1,
                              padding=(kernel_size-1)//2
                              )

        if forward == 'slicing':
            self.forward = self.forward_slicing
        elif forward == 'split_cat': # FasterNet used
            self.forward = self.forward_split_cat
        else:
            raise NotImplementedError

    def forward_slicing(self, x: Tensor) -> Tensor:
        """ Apply forward pass for for inference. """
        x = x.clone()   # !!! Keep the original input intact for the residual connection later
        x[:, :self.dim_conv, :, :] = self.conv(x[:, :self.dim_conv, :, :])

        return x

    def forward_split_cat(self, x: Tensor) -> Tensor:
        """ Apply forward pass for training/inference. """
        x1, x2 = torch.split(x, [self.dim_conv, self.dim_untouched], dim=1)
        x1 = self.conv(x1)
        x = torch.cat((x1, x2), 1)

        return x

class PDWConv(nn.Module):
    """Partial convolution (PConv)
    """
    def __init__(self, 
                 dim: int,
                 n_div: int = 4,
                 forward: str = 'split_cat',
                 kernel_size: int = 3):
        """ Construct a PConv layer.
        :param dim: Number of input/output channels.
        :param n_div: Peciprocal of the partial ratio. FasterNet use 4.
        :param forword: Forword type, can be either 'split_cat' or 'slicing'.
        :param kernel_size: Kernel size.
        """

        super().__init__()
        self.dim_conv = dim // n_div
        self.dim_untouched = dim - self.dim_conv
        self.conv = nn.Conv2d(self.dim_conv,
                              self.dim_conv,
                              kernel_size,
                              stride=1,
                              padding=(kernel_size-1)//2,
                              groups=self.dim_conv
                              )

        if forward == 'slicing':
            self.forward = self.forward_slicing
        elif forward == 'split_cat': # FasterNet used
            self.forward = self.forward_split_cat
        else:
            raise NotImplementedError

    def forward_slicing(self, x: Tensor) -> Tensor:
        """ Apply forward pass for for inference. """
        x = x.clone()   # !!! Keep the original input intact for the residual connection later
        x[:, :self.dim_conv, :, :] = self.conv(x[:, :self.dim_conv, :, :])

        return x

    def forward_split_cat(self, x: Tensor) -> Tensor:
        """ Apply forward pass for training/inference. """
        x1, x2 = torch.split(x, [self.dim_conv, self.dim_untouched], dim=1)
        x1 = self.conv(x1)
        x = torch.cat((x1, x2), 1)

        return x
    
class ConvNeXt(nn.Module):
    def __init__(self, network_type, num_classes):
        super(ConvNeXt, self).__init__()
        convnext = convnext_dict[network_type](pretrained=True)   
        in_features = convnext.classifier[2].in_features
        convnext.classifier[2] = nn.Linear(in_features, num_classes)
        self.model = convnext

    def forward(self, x):
        x = self.model(x)
        return x

class PC_ConvNeXt(nn.Module):
    def __init__(self, network_type, num_classes):
        super(PC_ConvNeXt, self).__init__()
        convnext = models.convnext_tiny(pretrained=False, stochastic_depth_prob=0.2)  
        # 1.PConv-BN
        '''
        for i, j, dim in zip([1,3,5,7],[3,3,9,3],[96,192,384,768]):
            for k in range(j):
                convnext.features[i][k].block[0] = PConv(dim, kernel_size=7)
                convnext.features[i][k].block[2] = convnext.features[i][k].block[1]
                convnext.features[i][k].block[1] = nn.BatchNorm2d(dim,eps=1e-6)
        
        # 2.PConv-LN
        '''
        for i, j, dim in zip([1,3,5,7],[3,3,9,3],[96,192,384,768]):
            for k in range(j):
                convnext.features[i][k].block[0] = PConv(dim, kernel_size=7)
        
        in_features = convnext.classifier[2].in_features
        convnext.classifier[2] = nn.Linear(in_features, num_classes)
        self.model = convnext
        del convnext

    def forward(self, x):
        x = self.model(x)
        return x


class PCb_ConvNeXt(nn.Module):
    def __init__(self):
        super(PCb_ConvNeXt, self).__init__()
        convnext = models.convnext_tiny(pretrained=False, stochastic_depth_prob=0.2,num_classes=100)  
        # 1.PConv-BN
        
        for i, j, dim in zip([1,3,5,7],[3,3,9,3],[96,192,384,768]):
            for k in range(j):
                convnext.features[i][k].block[0] = PConv(dim, kernel_size=7)
                convnext.features[i][k].block[2] = convnext.features[i][k].block[1]
                convnext.features[i][k].block[1] = nn.BatchNorm2d(dim,eps=1e-6)
        self.model = convnext
    def forward(self, x):
        x = self.model(x)
        return x
    

"""
class PCbn_ConvNeXt(nn.Module):
    def __init__(self, network_type, num_classes):
        super(PCbn_ConvNeXt, self).__init__()
        convnext = PCb_ConvNeXt()
        model_dir = 'checkpoint/torch_conv__dsCIFAR100__bs64__pc7_bn.pth'
        convnext.load_state_dict(torch.load(model_dir))
        '''
        #convnext = models.convnext_tiny(pretrained=False, stochastic_depth_prob=0.2)  
        # 1.PConv-BN
        
        for i, j, dim in zip([1,3,5,7],[3,3,9,3],[96,192,384,768]):
            for k in range(j):
                convnext.features[i][k].block[0] = PConv(dim, kernel_size=7)
                convnext.features[i][k].block[2] = convnext.features[i][k].block[1]
                convnext.features[i][k].block[1] = nn.BatchNorm2d(dim,eps=1e-6)
        
        # 2.PConv-LN
        '''
        '''
        for i, j, dim in zip([1,3,5,7],[3,3,9,3],[96,192,384,768]):
            for k in range(j):
                convnext.features[i][k].block[0] = PConv(dim, kernel_size=7)
        '''
        in_features = convnext.model.classifier[2].in_features
        convnext.model.classifier[2] = nn.Linear(in_features, num_classes)
        self.model = convnext
        del convnext

    def forward(self, x):
        x = self.model(x)
        return x
"""
class _PC_ConvNeXt(nn.Module):
    def __init__(self):
        super(_PC_ConvNeXt, self).__init__()
        convnext = models.convnext_tiny(pretrained=False, stochastic_depth_prob=0.2)  
        # 1.PConv-BN
        convnext.features[0][1] = nn.BatchNorm2d(96, eps=1e-6)
        for i, j, dim in zip([1,3,5,7],[3,3,9,3],[96,192,384,768]):
            if i > 1:
                convnext.features[i-1][0] = nn.BatchNorm2d(dim//2, eps=1e-6)
            for k in range(j):
                convnext.features[i][k].block[0] = PConv(dim, kernel_size=7)
                convnext.features[i][k].block[2] = convnext.features[i][k].block[1]
                convnext.features[i][k].block[1] = nn.BatchNorm2d(dim,eps=1e-6)
        
        # 2.PConv-LN
        '''
        for i, j, dim in zip([1,3,5,7],[3,3,9,3],[96,192,384,768]):
            for k in range(j):
                convnext.features[i][k].block[0] = PConv(dim, kernel_size=7)
        '''
        convnext.classifier[0] = nn.BatchNorm2d(768, eps=1e-6)
        in_features = convnext.classifier[2].in_features
        convnext.classifier[2] = nn.Linear(in_features, 10)
        self.model = convnext
        del convnext

    def forward(self, x):
            x = self.model(x)
            return x

class PCBN_ConvNeXt(nn.Module):
    def __init__(self, network_type, num_classes):
        super(PCBN_ConvNeXt, self).__init__()
        pc_convnext = _PC_ConvNeXt()

        model_dir = 'checkpoint/pretrained_model/PC_ConvNeXt_BN_0.1.pth'
        pc_convnext.load_state_dict(torch.load(model_dir))

        in_features = pc_convnext.model.classifier[2].in_features
        pc_convnext.model.classifier[2] = nn.Linear(in_features, num_classes)
        '''
        self.features = pc_convnext.model.features
        self.avgpool = pc_convnext.model.avgpool
        self.classifier = nn.Linear(768, num_classes)
        '''
        self.model = pc_convnext
        del pc_convnext

    def forward(self, x):
            x = self.model(x)
            #x = self.features(x)
            #x = self.avgpool(x)
            #x = x.view(x.size(0), -1)
            #x = self.classifier(x)
            return x

class PCbn_ConvNeXt(nn.Module):
    def __init__(self, network_type, num_classes):
        super(PCbn_ConvNeXt, self).__init__()
        convnext = models.convnext_tiny(pretrained=False, stochastic_depth_prob=0.2)  
        # 1.PConv-BN
        convnext.features[0][1] = nn.BatchNorm2d(96, eps=1e-6)
        for i, j, dim in zip([1,3,5,7],[3,3,9,3],[96,192,384,768]):
            if i > 1:
                convnext.features[i-1][0] = nn.BatchNorm2d(dim//2, eps=1e-6)
            for k in range(j):
                convnext.features[i][k].block[0] = PConv(dim, kernel_size=7)
                convnext.features[i][k].block[2] = convnext.features[i][k].block[1]
                convnext.features[i][k].block[1] = nn.BatchNorm2d(dim,eps=1e-6)
        
        # 2.PConv-LN
        '''
        for i, j, dim in zip([1,3,5,7],[3,3,9,3],[96,192,384,768]):
            for k in range(j):
                convnext.features[i][k].block[0] = PConv(dim, kernel_size=7)
        '''
        convnext.classifier[0] = nn.BatchNorm2d(768, eps=1e-6)
        in_features = convnext.classifier[2].in_features
        convnext.classifier[2] = nn.Linear(in_features, num_classes)
        
        self.model = convnext
        del convnext

    def forward(self, x):
            x = self.model(x)
            return x
"""
class PCbn_ConvNeXt(nn.Module):
    def __init__(self, network_type, num_classes):
        super(PCbn_ConvNeXt, self).__init__()
       
        convnext = models.convnext_tiny(pretrained=False, stochastic_depth_prob=0.2)  
        # 1.PConv-BN
        for i, j, dim in zip([1,3,5,7],[3,3,9,3],[96,192,384,768]):
            for k in range(j):
                convnext.features[i][k].block[0] = PConv(dim, kernel_size=7)
                convnext.features[i][k].block[2] = convnext.features[i][k].block[1]
                convnext.features[i][k].block[1] = nn.BatchNorm2d(dim,eps=1e-6)
        in_features = convnext.classifier[2].in_features
        convnext.classifier[2] = nn.Linear(in_features, num_classes)
        self.model = convnext
        del convnext

    def forward(self, x):
        x = self.model(x)
        return x

"""
class PDWC_ConvNeXt(nn.Module):
    def __init__(self, network_type, num_classes):
        super(PDWC_ConvNeXt, self).__init__()
        convnext = models.convnext_tiny(pretrained=False)  
        # 1.PConv-BN
        '''
        for i, j, dim in zip([1,3,5,7],[3,3,9,3],[96,192,384,768]):
            for k in range(j):
                convnext.features[i][k].block[0] = PConv(dim, kernel_size=7)
                convnext.features[i][k].block[2] = convnext.features[i][k].block[1]
                convnext.features[i][k].block[1] = nn.BatchNorm2d(dim,eps=1e-6)
        '''
        # 2.PConv-LN
        
        for i, j, dim in zip([1,3,5,7],[3,3,9,3],[96,192,384,768]):
            for k in range(j):
                convnext.features[i][k].block[0] = PDWConv(dim, kernel_size=7)
        
        in_features = convnext.classifier[2].in_features
        convnext.classifier[2] = nn.Linear(in_features, num_classes)
        self.model = convnext
        del convnext

    def forward(self, x):
        x = self.model(x)
        return x
    
class torchdpcbnNeXt(nn.Module):
    def __init__(self, network_type, num_classes):
        super(torchdpcbnNeXt, self).__init__()
        convnext = models.convnext_tiny(pretrained=False)  
        # 1.PConv-BN
     
        for i, j, dim in zip([1,3,5,7],[3,3,9,3],[96,192,384,768]):
            for k in range(j):
                convnext.features[i][k].block[0] = PDWConv(dim, kernel_size=7)
                convnext.features[i][k].block[2] = convnext.features[i][k].block[1]
                convnext.features[i][k].block[1] = nn.BatchNorm2d(dim,eps=1e-6)
        
        # 2.PConv-LN
        '''
        for i, j, dim in zip([1,3,5,7],[3,3,9,3],[96,192,384,768]):
            for k in range(j):
                convnext.features[i][k].block[0] = PConv(dim, kernel_size=7)
        '''
        in_features = convnext.classifier[2].in_features
        convnext.classifier[2] = nn.Linear(in_features, num_classes)
        self.model = convnext
        del convnext

    def forward(self, x):
        x = self.model(x)
        return x
    
class IConvNeXt(nn.Module):
    def __init__(self, network_type, num_classes):
        super(IConvNeXt, self).__init__()
        #convnext = convnext_dict[network_type](pretrained=False)
        #convnext = models.convnext_tiny(pretrained=False)
        convnext = convnext_tiny(pretrained=False, num_classes=num_classes)
        '''
        for i, j, z in zip([1,3,5,7],[3,3,9,3],[96,192,384,768]):
            for k in range(j):
                convnext.features[i][k].block[0] = PConv(z, kernel_size=3)
              
        in_features = convnext.classifier[2].in_features
        convnext.classifier[2] = nn.Linear(in_features, num_classes)
        '''
        self.model = convnext

    def forward(self, x):
        x = self.model(x)
        return x

def iconvnext_tiny(network_type, num_classes, pretrain = False):
    model = IConvNeXt(network_type, num_classes)
    if pretrain:
        model_dir = './checkpoint/iconvnext_tiny__dsCIFAR10__bs64_k7.pth'
        model.load_state_dict(torch.load(model_dir))
    return model
            