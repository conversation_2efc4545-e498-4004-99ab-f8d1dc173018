name: con_110
channels:
  - pytorch
  - defaults
dependencies:
  - _libgcc_mutex=0.1=main
  - _openmp_mutex=4.5=1_gnu
  - backcall=0.2.0=pyhd3eb1b0_0
  - blas=1.0=mkl
  - bzip2=1.0.8=h7b6447c_0
  - ca-certificates=2022.07.19=h06a4308_0
  - certifi=2022.9.24=py37h06a4308_0
  - cudatoolkit=11.3.1=h2bc3f7f_2
  - debugpy=1.5.1=py37h295c915_0
  - decorator=5.1.1=pyhd3eb1b0_0
  - entrypoints=0.4=py37h06a4308_0
  - faiss-gpu=1.7.2=py3.7_h28a55e0_0_cuda11.3
  - ffmpeg=4.3=hf484d3e_0
  - freetype=2.11.0=h70c0345_0
  - giflib=5.2.1=h7b6447c_0
  - gmp=6.2.1=h2531618_2
  - gnutls=3.6.15=he1e5248_0
  - intel-openmp=2021.4.0=h06a4308_3561
  - ipykernel=6.15.2=py37h06a4308_0
  - ipython=7.31.1=py37h06a4308_1
  - jedi=0.18.1=py37h06a4308_1
  - joblib=1.1.0=pyhd3eb1b0_0
  - jpeg=9d=h7f8727e_0
  - jupyter_client=7.1.2=pyhd3eb1b0_0
  - jupyter_core=4.11.1=py37h06a4308_0
  - lame=3.100=h7b6447c_0
  - lcms2=2.12=h3be6417_0
  - ld_impl_linux-64=2.35.1=h7274673_9
  - libfaiss=1.7.2=hfc2d529_0_cuda11.3
  - libffi=3.3=he6710b0_2
  - libgcc-ng=9.3.0=h5101ec6_17
  - libgfortran-ng=7.5.0=ha8ba4b0_17
  - libgfortran4=7.5.0=ha8ba4b0_17
  - libgomp=9.3.0=h5101ec6_17
  - libiconv=1.15=h63c8f33_5
  - libidn2=2.3.2=h7f8727e_0
  - libpng=1.6.37=hbc83047_0
  - libsodium=1.0.18=h7b6447c_0
  - libstdcxx-ng=9.3.0=hd4cf53a_17
  - libtasn1=4.16.0=h27cfd23_0
  - libtiff=4.2.0=h85742a9_0
  - libunistring=0.9.10=h27cfd23_0
  - libuv=1.40.0=h7b6447c_0
  - libwebp=1.2.2=h55f646e_0
  - libwebp-base=1.2.2=h7f8727e_0
  - lz4-c=1.9.3=h295c915_1
  - matplotlib-inline=0.1.6=py37h06a4308_0
  - mkl=2021.4.0=h06a4308_640
  - mkl-service=2.4.0=py37h7f8727e_0
  - mkl_fft=1.3.1=py37hd3c417c_0
  - mkl_random=1.2.2=py37h51133e4_0
  - ncurses=6.3=h7f8727e_2
  - nest-asyncio=1.5.5=py37h06a4308_0
  - nettle=3.7.3=hbbd107a_1
  - numpy=1.21.2=py37h20f2e39_0
  - numpy-base=1.21.2=py37h79a1101_0
  - olefile=0.46=py37_0
  - openh264=2.1.1=h4ff587b_0
  - openssl=1.1.1q=h7f8727e_0
  - packaging=21.3=pyhd3eb1b0_0
  - parso=0.8.3=pyhd3eb1b0_0
  - pexpect=4.8.0=pyhd3eb1b0_3
  - pickleshare=0.7.5=pyhd3eb1b0_1003
  - pillow=8.4.0=py37h5aabda8_0
  - pip=21.2.2=py37h06a4308_0
  - prompt-toolkit=3.0.20=pyhd3eb1b0_0
  - ptyprocess=0.7.0=pyhd3eb1b0_2
  - py=1.11.0=pyhd3eb1b0_0
  - pygments=2.11.2=pyhd3eb1b0_0
  - pyparsing=3.0.9=py37h06a4308_0
  - python=3.7.11=h12debd9_0
  - python-dateutil=2.8.2=pyhd3eb1b0_0
  - pytorch=1.10.2=py3.7_cuda11.3_cudnn8.2.0_0
  - pytorch-mutex=1.0=cuda
  - pyzmq=22.3.0=py37h295c915_2
  - readline=8.1.2=h7f8727e_1
  - scikit-learn=1.0.2=py37h51133e4_1
  - scipy=1.7.3=py37hc147768_0
  - setuptools=58.0.4=py37h06a4308_0
  - six=1.16.0=pyhd3eb1b0_1
  - sqlite=3.37.2=hc218d9a_0
  - threadpoolctl=2.2.0=pyh0d69192_0
  - tk=8.6.11=h1ccaba5_0
  - torchvision=0.11.3=py37_cu113
  - tornado=6.1=py37h27cfd23_0
  - tqdm=4.62.3=pyhd3eb1b0_1
  - traitlets=5.1.1=pyhd3eb1b0_0
  - typing_extensions=********=pyh06a4308_0
  - wcwidth=0.2.5=pyhd3eb1b0_0
  - wheel=0.37.1=pyhd3eb1b0_0
  - xz=5.2.5=h7b6447c_0
  - zeromq=4.3.4=h2531618_0
  - zlib=1.2.11=h7f8727e_4
  - zstd=1.4.9=haebb681_0
  - pip:
    - charset-normalizer==2.0.12
    - click==8.0.4
    - docker-pycreds==0.4.0
    - gitdb==4.0.9
    - gitpython==3.1.27
    - idna==3.3
    - importlib-metadata==4.11.1
    - pathtools==0.1.2
    - promise==2.3
    - protobuf==3.19.4
    - psutil==5.9.0
    - pyyaml==6.0
    - requests==2.27.1
    - sentry-sdk==1.5.6
    - shortuuid==1.0.8
    - smmap==5.0.0
    - termcolor==1.1.0
    - urllib3==1.26.8
    - wandb==0.12.10
    - yaspin==2.1.0
    - zipp==3.7.0
