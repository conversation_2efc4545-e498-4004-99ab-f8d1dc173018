# ConvNeXt V2 Atto configuration

# Model settings
model_name: convnextv2_atto

# Data settings
train_dataset: A_SLT
train: train
vali: val
test: test

# Training settings
batch_size: 16
n_epoch: 100
early_stop: 20

# Optimizer settings
lr: 0.0005
momentum: 0.9
weight_decay: 1e-5

# Learning rate scheduler settings
lr_gamma: 0.0003
lr_decay: 0.75
lr_scheduler: true

# Confusion matrix usage
confmtx: 0

# t-SNE visualization
t_sne: 0 