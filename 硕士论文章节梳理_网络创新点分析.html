<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>硕士论文创新点详细分析：基于改进ConvNeXt V2和TPTLN-GLC++的故障诊断网络</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 30px;
            font-size: 2.2em;
        }
        h2 {
            color: #34495e;
            border-left: 5px solid #e74c3c;
            padding-left: 15px;
            margin-top: 40px;
            font-size: 1.8em;
            background-color: #fdf2f2;
            padding: 15px;
            border-radius: 5px;
        }
        h3 {
            color: #2980b9;
            margin-top: 25px;
            font-size: 1.4em;
        }
        h4 {
            color: #27ae60;
            margin-top: 20px;
            font-size: 1.2em;
        }
        .innovation-detail {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px;
            margin: 25px 0;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .innovation-content {
            background-color: white;
            color: #333;
            padding: 20px;
            border-radius: 10px;
            margin-top: 15px;
        }
        .comparison-container {
            display: flex;
            gap: 20px;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        .before-after {
            flex: 1;
            min-width: 300px;
        }
        .before {
            background-color: #ffebee;
            border: 2px solid #f44336;
            padding: 20px;
            border-radius: 10px;
        }
        .after {
            background-color: #e8f5e8;
            border: 2px solid #4caf50;
            padding: 20px;
            border-radius: 10px;
        }
        .architecture-diagram {
            background-color: #f8f9fa;
            border: 2px dashed #6c757d;
            padding: 30px;
            text-align: center;
            margin: 20px 0;
            border-radius: 10px;
            font-family: monospace;
            font-size: 14px;
        }
        .flow-diagram {
            background: linear-gradient(90deg, #74b9ff, #0984e3);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .method-steps {
            background-color: #fff3e0;
            border-left: 5px solid #ff9800;
            padding: 20px;
            margin: 15px 0;
            border-radius: 5px;
        }
        .code-block {
            background-color: #1e1e1e;
            color: #d4d4d4;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Consolas', 'Monaco', monospace;
            overflow-x: auto;
            margin: 15px 0;
            border-left: 4px solid #007acc;
        }
        .brief-section {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border-left: 3px solid #6c757d;
        }
        .innovation-number {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 1.2em;
            margin-right: 15px;
        }
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .comparison-table th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            text-align: center;
        }
        .comparison-table td {
            padding: 12px;
            border: 1px solid #ddd;
            text-align: center;
        }
        .comparison-table tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        .improvement-highlight {
            background: linear-gradient(45deg, #56ab2f, #a8e6cf);
            color: white;
            padding: 8px 12px;
            border-radius: 20px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>硕士论文创新点详细分析：基于改进ConvNeXt V2和TPTLN-GLC++的故障诊断网络</h1>

        <!-- 简要概述 -->
        <div class="brief-section">
            <h3>📋 论文章节结构概述</h3>
            <p><strong>第三章：</strong>基于小波变换和注意力机制的轻量化ConvNeXt V2网络设计</p>
            <p><strong>第四章：</strong>理论引导渐进式迁移学习网络TPTLN-GLC++构建</p>
            <p><strong>第五章：</strong>故障诊断实验验证与性能分析</p>
        </div>

        <div class="brief-section">
            <h3>🎯 核心创新贡献</h3>
            <ul>
                <li><strong>网络架构创新：</strong>提出融合小波变换和EMA注意力的改进ConvNeXt V2</li>
                <li><strong>学习策略创新：</strong>设计理论引导渐进式迁移学习框架TPTLN-GLC++</li>
                <li><strong>聚类算法创新：</strong>开发全局-局部联合聚类机制提升伪标签质量</li>
            </ul>
        </div>

        <!-- 创新点1：小波变换卷积层 -->
        <h2><span class="innovation-number">1</span>创新点一：小波变换卷积层（WTConv2d）</h2>

        <div class="innovation-detail">
            <h3>🔬 创新归属：提出</h3>
            <h3>💡 创新思路：将传统深度卷积替换为小波变换卷积，增强多尺度特征提取能力</h3>

            <div class="innovation-content">
                <h4>📊 改进前后对比分析</h4>
                <div class="comparison-container">
                    <div class="before-after before">
                        <h4>🔴 原始ConvNeXt V2 Block</h4>
                        <div class="architecture-diagram">
Input (N, C, H, W)
        ↓
Depthwise Conv 7×7
(groups=C, 单一尺度)
        ↓
LayerNorm
        ↓
Linear (C → 4C)
        ↓
GELU + GRN
        ↓
Linear (4C → C)
        ↓
Output (N, C, H, W)
                        </div>
                        <p><strong>局限性：</strong></p>
                        <ul>
                            <li>只能提取空域特征</li>
                            <li>缺乏多尺度分析能力</li>
                            <li>对频域信息不敏感</li>
                        </ul>
                    </div>

                    <div class="before-after after">
                        <h4>🟢 改进后WTConv2d Block</h4>
                        <div class="architecture-diagram">
Input (N, C, H, W)
        ↓
Wavelet Transform
(LL, LH, HL, HH子带)
        ↓
Multi-scale Convolution
(4个子带并行处理)
        ↓
Inverse Wavelet Transform
        ↓
Base Conv + Scale
        ↓
Output (N, C, H, W)
                        </div>
                        <p><strong>优势：</strong></p>
                        <ul>
                            <li>时频域联合特征提取</li>
                            <li>多分辨率分析能力</li>
                            <li>自适应小波滤波器</li>
                        </ul>
                    </div>
                </div>

                <h4>🔧 具体设计方法</h4>
                <div class="method-steps">
                    <h5>步骤1：小波滤波器构建</h5>
                    <div class="code-block">
def create_wavelet_filter(wave, in_size, out_size, type=torch.float):
    w = pywt.Wavelet(wave)  # 选择小波基函数（如'db1', 'haar'等）

    # 分解滤波器（低通和高通）
    dec_hi = torch.tensor(w.dec_hi[::-1], dtype=type)  # 高频分解滤波器
    dec_lo = torch.tensor(w.dec_lo[::-1], dtype=type)  # 低频分解滤波器

    # 构建四个子带滤波器：LL, LH, HL, HH
    dec_filters = torch.stack([
        dec_lo.unsqueeze(0) * dec_lo.unsqueeze(1),  # LL (低频-低频)
        dec_lo.unsqueeze(0) * dec_hi.unsqueeze(1),  # LH (低频-高频)
        dec_hi.unsqueeze(0) * dec_lo.unsqueeze(1),  # HL (高频-低频)
        dec_hi.unsqueeze(0) * dec_hi.unsqueeze(1)   # HH (高频-高频)
    ], dim=0)

    return dec_filters, rec_filters  # 返回分解和重构滤波器
                    </div>
                </div>

                <div class="method-steps">
                    <h5>步骤2：WTConv2d核心实现</h5>
                    <div class="code-block">
class WTConv2d(nn.Module):
    def __init__(self, in_channels, out_channels, kernel_size=5,
                 stride=1, bias=True, wt_levels=1, wt_type='db1'):
        super(WTConv2d, self).__init__()

        # 创建小波滤波器（可学习参数）
        self.wt_filter, self.iwt_filter = create_wavelet_filter(
            wt_type, in_channels, in_channels, torch.float)
        self.wt_filter = nn.Parameter(self.wt_filter, requires_grad=False)

        # 基础卷积层（处理低频信息）
        self.base_conv = nn.Conv2d(in_channels, in_channels,
                                  kernel_size, padding='same',
                                  stride=1, groups=in_channels, bias=bias)
        self.base_scale = _ScaleModule([1, in_channels, 1, 1])

        # 小波卷积层（处理多尺度信息）
        self.wavelet_convs = nn.ModuleList([
            nn.Conv2d(in_channels*4, in_channels*4, kernel_size,
                     padding='same', stride=1, groups=in_channels*4, bias=False)
            for _ in range(self.wt_levels)
        ])

        # 可学习的缩放因子
        self.wavelet_scale = nn.ModuleList([
            _ScaleModule([1, in_channels*4, 1, 1], init_scale=0.1)
            for _ in range(self.wt_levels)
        ])
                    </div>
                </div>
            </div>
        </div>

        <!-- 创新点2：EMA注意力机制 -->
        <h2><span class="innovation-number">2</span>创新点二：EMA注意力机制集成</h2>

        <div class="innovation-detail">
            <h3>🔬 创新归属：引入并改进</h3>
            <h3>💡 创新思路：在ConvNeXt V2的Block中集成EMA注意力机制，提升特征选择性</h3>

            <div class="innovation-content">
                <h4>📊 EMA注意力机制设计对比</h4>
                <div class="comparison-container">
                    <div class="before-after before">
                        <h4>🔴 原始ConvNeXt V2（无注意力）</h4>
                        <div class="architecture-diagram">
Input Features (N, C, H, W)
        ↓
Pointwise Conv (C → 4C)
        ↓
GELU Activation
        ↓
GRN (Global Response Norm)
        ↓
Pointwise Conv (4C → C)
        ↓
Output (N, C, H, W)
                        </div>
                        <p><strong>问题：</strong></p>
                        <ul>
                            <li>缺乏空间注意力机制</li>
                            <li>通道间关系建模不足</li>
                            <li>特征选择性较弱</li>
                        </ul>
                    </div>

                    <div class="before-after after">
                        <h4>🟢 集成EMA注意力后</h4>
                        <div class="architecture-diagram">
Input Features (N, C, H, W)
        ↓
Pointwise Conv (C → 4C)
        ↓
GELU Activation
        ↓
GRN (Global Response Norm)
        ↓
<span class="improvement-highlight">EMA Attention Module</span>
├─ Adaptive Pooling (H×1, 1×W)
├─ Group Normalization
├─ 1×1 Conv + Sigmoid
└─ Feature Recalibration
        ↓
Pointwise Conv (4C → C)
        ↓
Output (N, C, H, W)
                        </div>
                        <p><strong>改进：</strong></p>
                        <ul>
                            <li>增强空间特征选择</li>
                            <li>提升通道间交互</li>
                            <li>自适应特征重标定</li>
                        </ul>
                    </div>
                </div>

                <h4>🔧 EMA注意力机制具体实现</h4>
                <div class="method-steps">
                    <h5>步骤1：多尺度自适应池化</h5>
                    <div class="code-block">
class EMA(nn.Module):
    def __init__(self, channels, c2=None, factor=32):
        super(EMA, self).__init__()
        self.groups = factor
        assert channels // self.groups > 0

        # 多方向自适应池化
        self.agp = nn.AdaptiveAvgPool2d((1, 1))      # 全局池化
        self.pool_h = nn.AdaptiveAvgPool2d((None, 1)) # 水平池化
        self.pool_w = nn.AdaptiveAvgPool2d((1, None)) # 垂直池化

        # 分组归一化和卷积
        self.gn = nn.GroupNorm(channels // self.groups, channels // self.groups)
        self.conv1x1 = nn.Conv2d(channels // self.groups, channels // self.groups,
                                kernel_size=1, stride=1, padding=0)
        self.conv3x3 = nn.Conv2d(channels // self.groups, channels // self.groups,
                                kernel_size=3, stride=1, padding=1)
                    </div>
                </div>

                <div class="method-steps">
                    <h5>步骤2：注意力权重计算</h5>
                    <div class="code-block">
    def forward(self, x):
        b, c, h, w = x.size()
        # 分组处理，增强计算效率
        group_x = x.reshape(b * self.groups, -1, h, w)  # (b*g, c//g, h, w)

        # 多方向池化获取空间信息
        x_h = self.pool_h(group_x)                      # (b*g, c//g, h, 1)
        x_w = self.pool_w(group_x).permute(0, 1, 3, 2)  # (b*g, c//g, w, 1)

        # 拼接并通过1×1卷积融合
        hw = self.conv1x1(torch.cat([x_h, x_w], dim=2))  # (b*g, c//g, h+w, 1)
        x_h, x_w = torch.split(hw, [h, w], dim=2)        # 分离H和W维度

        # 生成空间注意力权重
        x1 = self.gn(group_x * x_h.sigmoid() * x_w.permute(0, 1, 3, 2).sigmoid())
        x2 = self.conv3x3(group_x)

        # 通道注意力计算
        x11 = self.softmax(self.agp(x1).reshape(b * self.groups, -1, 1).permute(0, 2, 1))
        x12 = x2.reshape(b * self.groups, c // self.groups, -1)
        x21 = self.softmax(self.agp(x2).reshape(b * self.groups, -1, 1).permute(0, 2, 1))
        x22 = x1.reshape(b * self.groups, c // self.groups, -1)

        # 最终注意力权重
        weights = (torch.matmul(x11, x12) + torch.matmul(x21, x22)).reshape(b * self.groups, 1, h, w)
        return (group_x * weights.sigmoid()).reshape(b, c, h, w)
                    </div>
                </div>

                <h4>🎯 EMA注意力的创新优势</h4>
                <div class="flow-diagram">
                    <h5>创新机制流程图</h5>
                    <div style="text-align: center; font-family: monospace;">
                        输入特征 → 分组处理 → 多方向池化 → 空间注意力 → 通道注意力 → 特征重标定 → 输出
                        <br><br>
                        <strong>关键创新：</strong><br>
                        ✓ 分组处理降低计算复杂度<br>
                        ✓ 多方向池化捕获空间依赖<br>
                        ✓ 空间-通道联合注意力<br>
                        ✓ 自适应特征重标定机制
                    </div>
                </div>
            </div>
        </div>

        <!-- 创新点3：TPTLN-GLC++聚类策略 -->
        <h2><span class="innovation-number">3</span>创新点三：改进的TPTLN-GLC++聚类策略</h2>

        <div class="innovation-detail">
            <h3>🔬 创新归属：设计并改进</h3>
            <h3>💡 创新思路：结合全局聚类和局部聚类，提升无监督域适应中的伪标签质量</h3>

            <div class="innovation-content">
                <h4>📊 原始GLC++与改进TPTLN-GLC++对比</h4>
                <div class="comparison-container">
                    <div class="before-after before">
                        <h4>🔴 原始GLC++方法</h4>
                        <div class="architecture-diagram">
目标域特征提取
        ↓
简单全局聚类
(K-means聚类)
        ↓
伪标签生成
(硬标签分配)
        ↓
域对抗训练
(单一判别器)
        ↓
分类器更新
                        </div>
                        <p><strong>局限性：</strong></p>
                        <ul>
                            <li>聚类策略单一</li>
                            <li>伪标签质量不高</li>
                            <li>缺乏渐进式学习</li>
                            <li>域对抗训练不稳定</li>
                        </ul>
                    </div>

                    <div class="before-after after">
                        <h4>🟢 改进TPTLN-GLC++</h4>
                        <div class="architecture-diagram">
改进ConvNeXt V2特征提取
        ↓
<span class="improvement-highlight">One-vs-All全局聚类</span>
(每类别二分类器)
        ↓
<span class="improvement-highlight">Mini-batch KMeans局部聚类</span>
(高置信度样本细化)
        ↓
<span class="improvement-highlight">对比学习增强</span>
(特征判别性提升)
        ↓
<span class="improvement-highlight">渐进式训练</span>
(Distract + Attract阶段)
        ↓
<span class="improvement-highlight">多判别器协同</span>
(5个并行判别器)
                        </div>
                        <p><strong>改进优势：</strong></p>
                        <ul>
                            <li>多层次聚类策略</li>
                            <li>高质量伪标签生成</li>
                            <li>理论引导训练</li>
                            <li>稳定的域对抗</li>
                        </ul>
                    </div>
                </div>

                <h4>🔧 TPTLN-GLC++核心算法设计</h4>
                <div class="method-steps">
                    <h5>算法1：One-vs-All全局聚类</h5>
                    <div class="code-block">
class OneVsAllLabelGenerator(nn.Module):
    """逐类伪标签生成器"""
    def __init__(self, feature_dim, num_classes):
        super(OneVsAllLabelGenerator, self).__init__()
        self.num_classes = num_classes

        # 为每个类别创建一个二分类器
        self.binary_classifiers = nn.ModuleList([
            nn.Sequential(
                nn.Linear(feature_dim, 256),
                nn.BatchNorm1d(256),
                nn.ReLU(inplace=True),
                nn.Linear(256, 1),
                nn.Sigmoid()  # 输出0-1概率
            ) for _ in range(num_classes)
        ])

    def forward(self, x):
        # 对每个类别生成二分类结果
        binary_outputs = []
        for i in range(self.num_classes):
            binary_outputs.append(self.binary_classifiers[i](x))

        # 将所有二分类结果拼接成一个向量
        return torch.cat(binary_outputs, dim=1)  # (N, num_classes)
                    </div>
                </div>

                <div class="method-steps">
                    <h5>算法2：全局-局部联合聚类</h5>
                    <div class="code-block">
@torch.no_grad()
def generate_pseudo_labels(self, target_loader, device, epoch_idx=0):
    """生成目标域的伪标签"""
    self.eval()

    # 步骤1：收集所有目标域特征和预测
    all_features, all_binary_preds = [], []
    for data, _ in target_loader:
        data = data.to(device)
        outputs = self(data)
        all_features.append(outputs['features'].cpu())
        all_binary_preds.append(outputs['binary_preds'].cpu())

    all_features = F.normalize(torch.cat(all_features, dim=0), dim=1)
    all_binary_preds = torch.cat(all_binary_preds, dim=0)

    # 步骤2：全局聚类 - One-vs-All策略
    data_num = all_features.shape[0]
    pos_topk_num = int(data_num / self.num_classes / 3)  # 每类高置信度样本数

    sorted_binary_preds, sorted_indices = torch.sort(all_binary_preds, dim=0, descending=True)
    pseudo_labels = torch.zeros(data_num, self.num_classes)

    for cls_idx in range(self.num_classes):
        # 获取当前类别的高置信度样本
        pos_indices = sorted_indices[:pos_topk_num, cls_idx]
        pos_features = all_features[pos_indices]

        # 计算类别原型（中心）
        class_prototype = F.normalize(pos_features.mean(dim=0, keepdim=True), dim=1)

        # 计算所有样本与类别原型的相似度
        similarity = torch.mm(all_features, class_prototype.t()).squeeze()
        pseudo_labels[:, cls_idx] = similarity * (1.0 - self.rho) + self.rho

    # 步骤3：局部聚类 - Mini-batch KMeans细化
    for cls_idx in range(self.num_classes):
        high_conf_mask = (pseudo_labels[:, cls_idx] > 0.7)
        if high_conf_mask.sum() > 50:
            high_conf_features = all_features[high_conf_mask].numpy()

            # 使用Mini-batch KMeans进行局部聚类
            n_clusters = min(5, high_conf_mask.sum() // 10)
            if n_clusters > 1:
                kmeans = MiniBatchKMeans(n_clusters=n_clusters, random_state=0,
                                       batch_size=100, n_init=3)
                cluster_labels = kmeans.fit_predict(high_conf_features)
                cluster_centers = torch.tensor(kmeans.cluster_centers_, dtype=torch.float32)

                # 根据聚类结果调整伪标签
                for i, idx in enumerate(torch.where(high_conf_mask)[0]):
                    cluster_idx = cluster_labels[i]
                    sample_feature = all_features[idx].unsqueeze(0)
                    center_feature = cluster_centers[cluster_idx].unsqueeze(0)
                    similarity = F.cosine_similarity(sample_feature, center_feature).item()

                    if similarity <= 0.8:  # 低相似度，降低置信度
                        pseudo_labels[idx, cls_idx] *= 0.5

    return pseudo_labels
                    </div>
                </div>
            </div>
        </div>

        <!-- 创新点4：渐进式学习策略 -->
        <h2><span class="innovation-number">4</span>创新点四：理论引导的渐进式训练策略</h2>

        <div class="innovation-detail">
            <h3>🔬 创新归属：提出</h3>
            <h3>💡 创新思路：将训练过程分为Distract和Attract两个阶段，逐步提升域适应效果</h3>

            <div class="innovation-content">
                <h4>📊 传统训练与渐进式训练对比</h4>
                <div class="comparison-container">
                    <div class="before-after before">
                        <h4>🔴 传统端到端训练</h4>
                        <div class="architecture-diagram">
初始化网络参数
        ↓
同时进行：
├─ 源域分类训练
├─ 域对抗训练
└─ 目标域伪标签学习
        ↓
联合优化所有损失
        ↓
容易陷入局部最优
                        </div>
                        <p><strong>问题：</strong></p>
                        <ul>
                            <li>多任务冲突严重</li>
                            <li>训练不稳定</li>
                            <li>容易过拟合</li>
                            <li>收敛速度慢</li>
                        </ul>
                    </div>

                    <div class="before-after after">
                        <h4>🟢 渐进式两阶段训练</h4>
                        <div class="architecture-diagram">
<span class="improvement-highlight">阶段1：Distract（分离）</span>
├─ 分离已知/未知类别
├─ 建立可靠伪标签
└─ 初步域对抗训练
        ↓
<span class="improvement-highlight">阶段2：Attract（吸引）</span>
├─ 对齐已知类别特征
├─ 精细化伪标签
└─ 强化域对抗训练
        ↓
<span class="improvement-highlight">多判别器协同</span>
5个并行判别器稳定训练
                        </div>
                        <p><strong>优势：</strong></p>
                        <ul>
                            <li>理论指导训练</li>
                            <li>阶段性目标明确</li>
                            <li>训练过程稳定</li>
                            <li>收敛效果更好</li>
                        </ul>
                    </div>
                </div>

                <h4>🔧 渐进式训练具体实现</h4>
                <div class="method-steps">
                    <h5>阶段1：Distract阶段实现</h5>
                    <div class="code-block">
def distract_phase_training(model, source_loader, target_loader, optimizer, args):
    """Distract阶段：分离已知类别和未知类别"""
    print("第一阶段：Distract - 分离已知类别和未知类别")

    for epoch in range(args.distract_epochs):  # 通常设置为总epochs的1/3
        model.train()

        # 更新目标域伪标签
        if epoch % args.pseudo_label_update_freq == 0:
            print(f"更新目标域伪标签 (Epoch {epoch+1})")
            pseudo_result = model.generate_pseudo_labels(target_loader, args.device, epoch)
            known_indices = pseudo_result['known_indices']
            unknown_indices = pseudo_result['unknown_indices']

            print(f"已知类别样本数: {len(known_indices)}, 未知类别样本数: {len(unknown_indices)}")

        # 训练循环
        for batch_idx, (source_data, target_data) in enumerate(zip(source_loader, target_loader)):
            source_x, source_y = source_data[0].to(args.device), source_data[1].to(args.device)
            target_x = target_data[0].to(args.device)

            # 前向传播
            source_outputs = model(source_x, domain='source')
            target_outputs = model(target_x, domain='target')

            # 损失计算
            # 1. 源域分类损失
            cls_loss = F.cross_entropy(source_outputs['logits'], source_y)

            # 2. 域对抗损失（较小权重）
            domain_loss = compute_domain_adversarial_loss(
                source_outputs['domain_pred'], target_outputs['domain_pred'])

            # 3. 多判别器损失
            multi_domain_loss = compute_multi_discriminator_loss(
                source_outputs['multi_domain_pred'], target_outputs['multi_domain_pred'])

            # 4. 伪标签损失（仅对已知类别）
            if len(known_indices) > 0:
                known_target_outputs = target_outputs['logits'][known_indices]
                known_pseudo_labels = pseudo_result['pseudo_labels'][known_indices]
                pseudo_loss = F.cross_entropy(known_target_outputs, known_pseudo_labels.argmax(dim=1))
            else:
                pseudo_loss = torch.tensor(0.0).to(args.device)

            # 总损失（Distract阶段域对抗权重较小）
            total_loss = cls_loss + 0.1 * domain_loss + 0.1 * multi_domain_loss + 0.5 * pseudo_loss

            # 反向传播
            optimizer.zero_grad()
            total_loss.backward()
            optimizer.step()
                    </div>
                </div>

                <div class="method-steps">
                    <h5>阶段2：Attract阶段实现</h5>
                    <div class="code-block">
def attract_phase_training(model, source_loader, target_loader, optimizer, args):
    """Attract阶段：对齐源域和目标域的已知类别特征分布"""
    print("第二阶段：Attract - 对齐已知类别特征")

    for epoch in range(args.attract_epochs):  # 通常设置为总epochs的2/3
        model.train()

        # 更新目标域伪标签（更频繁）
        if epoch % (args.pseudo_label_update_freq // 2) == 0:
            pseudo_result = model.generate_pseudo_labels(target_loader, args.device, epoch)
            known_indices = pseudo_result['known_indices']

        for batch_idx, (source_data, target_data) in enumerate(zip(source_loader, target_loader)):
            source_x, source_y = source_data[0].to(args.device), source_data[1].to(args.device)
            target_x = target_data[0].to(args.device)

            # 前向传播
            source_outputs = model(source_x, domain='source')
            target_outputs = model(target_x, domain='target')

            # 损失计算
            # 1. 源域分类损失
            cls_loss = F.cross_entropy(source_outputs['logits'], source_y)

            # 2. 域对抗损失（增大权重）
            domain_loss = compute_domain_adversarial_loss(
                source_outputs['domain_pred'], target_outputs['domain_pred'])

            # 3. 多判别器损失（增大权重）
            multi_domain_loss = compute_multi_discriminator_loss(
                source_outputs['multi_domain_pred'], target_outputs['multi_domain_pred'])

            # 4. 对比学习损失（新增）
            contrastive_loss = model.contrastive_loss(
                target_outputs['proj_features'],
                pseudo_result['pseudo_labels'],
                known_indices)

            # 5. 精细化伪标签损失
            if len(known_indices) > 0:
                known_target_outputs = target_outputs['logits'][known_indices]
                known_pseudo_labels = pseudo_result['pseudo_labels'][known_indices]
                pseudo_loss = F.cross_entropy(known_target_outputs, known_pseudo_labels.argmax(dim=1))
            else:
                pseudo_loss = torch.tensor(0.0).to(args.device)

            # 总损失（Attract阶段域对抗权重增大）
            total_loss = cls_loss + 1.0 * domain_loss + 0.5 * multi_domain_loss + \
                        1.0 * pseudo_loss + 0.3 * contrastive_loss

            # 反向传播
            optimizer.zero_grad()
            total_loss.backward()
            optimizer.step()
                    </div>
                </div>

                <h4>🎯 渐进式训练的理论基础</h4>
                <div class="flow-diagram">
                    <h5>理论引导的训练策略</h5>
                    <div style="text-align: left; font-family: monospace;">
                        <strong>理论依据：</strong><br>
                        • <strong>分离原理：</strong>先区分已知和未知类别，避免负迁移<br>
                        • <strong>渐进对齐：</strong>逐步增强域对抗训练强度<br>
                        • <strong>多判别器理论：</strong>并行判别器提供更稳定的梯度<br>
                        • <strong>对比学习理论：</strong>增强同类聚合，异类分离<br><br>

                        <strong>训练策略：</strong><br>
                        📍 <strong>Distract阶段：</strong>λ_domain = 0.1, λ_pseudo = 0.5<br>
                        📍 <strong>Attract阶段：</strong>λ_domain = 1.0, λ_pseudo = 1.0, λ_contrastive = 0.3<br>
                        📍 <strong>多判别器：</strong>5个并行判别器，权重λ_multi = 0.1~0.5
                    </div>
                </div>
            </div>
        </div>

        <!-- 创新点总结与对比 -->
        <h2><span class="innovation-number">📊</span>创新点综合对比与总结</h2>

        <div class="innovation-detail">
            <h3>🎯 四大创新点技术对比表</h3>

            <div class="innovation-content">
                <table class="comparison-table">
                    <tr>
                        <th>创新点</th>
                        <th>创新归属</th>
                        <th>技术难度</th>
                        <th>理论贡献</th>
                        <th>实用价值</th>
                        <th>核心优势</th>
                    </tr>
                    <tr>
                        <td><strong>小波变换卷积层</strong></td>
                        <td>提出</td>
                        <td>⭐⭐⭐⭐⭐</td>
                        <td>⭐⭐⭐⭐</td>
                        <td>⭐⭐⭐⭐⭐</td>
                        <td>时频域联合特征提取</td>
                    </tr>
                    <tr>
                        <td><strong>EMA注意力集成</strong></td>
                        <td>引入改进</td>
                        <td>⭐⭐⭐</td>
                        <td>⭐⭐⭐</td>
                        <td>⭐⭐⭐⭐</td>
                        <td>空间-通道联合注意力</td>
                    </tr>
                    <tr>
                        <td><strong>GLC++聚类策略</strong></td>
                        <td>设计改进</td>
                        <td>⭐⭐⭐⭐⭐</td>
                        <td>⭐⭐⭐⭐⭐</td>
                        <td>⭐⭐⭐⭐⭐</td>
                        <td>全局-局部联合聚类</td>
                    </tr>
                    <tr>
                        <td><strong>渐进式训练策略</strong></td>
                        <td>提出</td>
                        <td>⭐⭐⭐⭐</td>
                        <td>⭐⭐⭐⭐⭐</td>
                        <td>⭐⭐⭐⭐⭐</td>
                        <td>理论引导两阶段训练</td>
                    </tr>
                </table>

                <h4>🏆 创新点贡献度量化分析</h4>
                <div class="comparison-container">
                    <div class="before-after">
                        <h4>📈 技术创新程度排序</h4>
                        <ol>
                            <li><span class="improvement-highlight">GLC++聚类策略</span> - 最高创新度</li>
                            <li><span class="improvement-highlight">小波变换卷积层</span> - 高创新度</li>
                            <li><span class="improvement-highlight">渐进式训练策略</span> - 高创新度</li>
                            <li><span class="improvement-highlight">EMA注意力集成</span> - 中等创新度</li>
                        </ol>
                    </div>

                    <div class="before-after">
                        <h4>🎯 实用价值排序</h4>
                        <ol>
                            <li><span class="improvement-highlight">小波变换卷积层</span> - 最高实用价值</li>
                            <li><span class="improvement-highlight">GLC++聚类策略</span> - 高实用价值</li>
                            <li><span class="improvement-highlight">渐进式训练策略</span> - 高实用价值</li>
                            <li><span class="improvement-highlight">EMA注意力集成</span> - 中等实用价值</li>
                        </ol>
                    </div>
                </div>

                <h4>🔬 创新机制协同效应图</h4>
                <div class="flow-diagram">
                    <div style="text-align: center; font-family: monospace; font-size: 16px;">
                        <strong>四大创新点协同工作流程</strong><br><br>

                        <span class="improvement-highlight">小波变换卷积层</span><br>
                        ↓ (提供多尺度特征)<br>
                        <span class="improvement-highlight">EMA注意力机制</span><br>
                        ↓ (增强特征选择性)<br>
                        <span class="improvement-highlight">GLC++聚类策略</span><br>
                        ↓ (生成高质量伪标签)<br>
                        <span class="improvement-highlight">渐进式训练策略</span><br>
                        ↓ (稳定优化过程)<br>
                        <strong>🎯 最终故障诊断结果</strong>
                    </div>
                </div>
            </div>
        </div>

        <!-- 简要章节概述 -->
        <div class="brief-section">
            <h2>📚 其他章节简要概述</h2>
            <p><strong>第一章 绪论：</strong>研究背景、意义、现状分析、论文贡献</p>
            <p><strong>第二章 理论基础：</strong>ConvNeXt V2、小波变换、注意力机制、域适应、聚类算法理论</p>
            <p><strong>第五章 实验验证：</strong>数据集介绍、实验设置、消融实验、对比实验、结果分析</p>
            <p><strong>第六章 总结展望：</strong>工作总结、创新点总结、不足与展望</p>
        </div>

        <!-- 最终创新贡献声明 -->
        <div class="innovation-detail">
            <h2>🏅 核心学术贡献与创新价值</h2>

            <div class="innovation-content">
                <h3>🎓 硕士论文核心贡献</h3>
                <div class="method-steps">
                    <h4>理论贡献</h4>
                    <ul>
                        <li><strong>小波-深度学习融合理论：</strong>首次将小波变换系统性地融入ConvNeXt V2架构，丰富了多尺度特征提取理论</li>
                        <li><strong>渐进式域适应理论：</strong>提出理论引导的两阶段训练策略，为跨域故障诊断提供新的理论框架</li>
                        <li><strong>全局-局部聚类理论：</strong>建立了多层次聚类的理论基础，提升了无监督域适应的理论深度</li>
                    </ul>
                </div>

                <div class="method-steps">
                    <h4>方法贡献</h4>
                    <ul>
                        <li><strong>WTConv2d卷积层：</strong>设计了可学习的小波变换卷积层，实现时频域联合特征提取</li>
                        <li><strong>TPTLN-GLC++框架：</strong>构建了完整的渐进式迁移学习网络，集成多种先进技术</li>
                        <li><strong>多判别器协同机制：</strong>开发了稳定的域对抗训练策略，提升训练稳定性</li>
                    </ul>
                </div>

                <div class="method-steps">
                    <h4>应用贡献</h4>
                    <ul>
                        <li><strong>轻量化设计：</strong>ConvNeXt V2 Atto版本满足工业实时诊断需求</li>
                        <li><strong>跨域适应能力：</strong>解决了不同工况下的故障诊断迁移问题</li>
                        <li><strong>实用性验证：</strong>在多个故障诊断数据集上验证了方法的有效性</li>
                    </ul>
                </div>

                <h3>📝 论文写作重点建议</h3>
                <div class="highlight">
                    <h4>创新点表述策略：</h4>
                    <ul>
                        <li><strong>突出首创性：</strong>"首次将小波变换引入ConvNeXt V2架构"</li>
                        <li><strong>强调理论性：</strong>"提出理论引导的渐进式训练策略"</li>
                        <li><strong>体现系统性：</strong>"构建了完整的TPTLN-GLC++框架"</li>
                        <li><strong>展现实用性：</strong>"轻量化设计满足工业应用需求"</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
