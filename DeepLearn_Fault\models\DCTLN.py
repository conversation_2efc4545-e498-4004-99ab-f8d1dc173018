import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.autograd.variable import *
import os
import numpy as np

class Classifier(nn.Module):
    """分类器网络"""
    def __init__(self, in_dim, out_dim):
        super(Classifier, self).__init__()
        self.fc = nn.Linear(in_dim, out_dim)
        self.main = nn.Sequential(
            self.fc,
            nn.Softmax(dim=-1)
        )

    def forward(self, x):
        out = [x]
        for module in self.main.children():
            x = module(x)
            out.append(x)
        return out

class DCTLN(nn.Module):
    """深度相关性迁移学习网络 (Deep Correlation Transfer Learning Network)

    通过最大化源域和目标域特征的相关性来实现迁移学习
    """
    def __init__(self, feature_extractor, num_classes, feature_dim=320):
        super(DCTLN, self).__init__()
        self.feature_extractor = feature_extractor

        # 移除特征提取器的分类头
        if hasattr(self.feature_extractor, 'head'):
            self.feature_extractor.head = nn.Identity()

        # 分类器
        self.classifier = Classifier(feature_dim, num_classes)

        # 特征变换层，用于计算相关性
        self.transform = nn.Sequential(
            nn.Linear(feature_dim, 256),
            nn.ReLU(),
            nn.Linear(256, 256)
        )

        # 域判别器
        self.domain_classifier = nn.Sequential(
            nn.Linear(feature_dim, feature_dim // 2),
            nn.ReLU(),
            nn.Dropout(0.5),
            nn.Linear(feature_dim // 2, 1),
            nn.Sigmoid()
        )

    def forward(self, x, domain='source'):
        # 提取特征
        features = self.feature_extractor(x)

        # 特征变换，用于计算相关性
        transformed_features = self.transform(features)

        # 分类
        cls_out = self.classifier(features)

        # 域判别
        domain_pred = self.domain_classifier(features)

        return {
            'features': features,
            'transformed_features': transformed_features,
            'logits': cls_out[1],  # softmax后的输出
            'embeddings': cls_out[0],  # 分类器输入的特征
            'domain_pred': domain_pred  # 域判别输出
        }

    def get_features(self, x):
        """仅提取特征，用于t-SNE可视化等"""
        return self.feature_extractor(x)

    def correlation_loss(self, source_features, target_features):
        """计算源域和目标域特征的相关性损失"""
        # 中心化特征
        source_features = source_features - torch.mean(source_features, dim=0)
        target_features = target_features - torch.mean(target_features, dim=0)

        # 计算协方差矩阵
        source_cov = torch.mm(source_features.t(), source_features) / (source_features.size(0) - 1)
        target_cov = torch.mm(target_features.t(), target_features) / (target_features.size(0) - 1)

        # 计算相关性损失（Frobenius范数）
        correlation_loss = torch.norm(source_cov - target_cov, p='fro')

        return correlation_loss

def create_DCTLN_model(model_name, num_classes, feature_extractor):
    """
    创建DCTLN模型

    Args:
        model_name: 模型名称
        num_classes: 分类类别数
        feature_extractor: 特征提取器

    Returns:
        DCTLN模型实例
    """
    # 获取特征维度，对于convnextv2_atto是320
    feature_dim = 320

    model = DCTLN(feature_extractor, num_classes, feature_dim)

    # 打印创建的模型信息
    print(f"成功创建DCTLN模型，特征提取器基于ConvNeXtV2 Atto，特征维度: {feature_dim}")
    print("注意：特征提取器的预训练权重已通过ConvNeXtV2_atto函数加载")

    return model
