import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.autograd.variable import *
import os
import numpy as np
import faiss
from sklearn.cluster import MiniBatchKMeans

class GradientReverseLayer(torch.autograd.Function):
    @staticmethod
    def forward(ctx, coeff, input):
        ctx.coeff = coeff
        return input

    @staticmethod
    def backward(ctx, grad_outputs):
        coeff = ctx.coeff
        return None, -coeff * grad_outputs

class GradientReverseModule(nn.Module):
    def __init__(self, scheduler):
        super(GradientReverseModule, self).__init__()
        self.scheduler = scheduler
        self.global_step = 0.0
        self.coeff = 0.0
        self.grl = GradientReverseLayer.apply

    def forward(self, x):
        self.coeff = self.scheduler(self.global_step)
        self.global_step += 1.0
        return self.grl(self.coeff, x)

def aToBSheduler(step, A, B, gamma=10, max_iter=10000):
    """从A值平滑过渡到B值的学习率调度器"""
    ans = A + (2.0 / (1 + np.exp(- gamma * step * 1.0 / max_iter)) - 1.0) * (B - A)
    return float(ans)

class Classifier(nn.Module):
    """分类器网络"""
    def __init__(self, in_dim, out_dim):
        super(Classifier, self).__init__()
        self.fc = nn.Linear(in_dim, out_dim)
        self.main = nn.Sequential(
            self.fc,
            nn.Softmax(dim=-1)
        )

    def forward(self, x):
        out = [x]
        for module in self.main.children():
            x = module(x)
            out.append(x)
        return out

class DomainDiscriminator(nn.Module):
    """域判别器网络"""
    def __init__(self, in_feature):
        super(DomainDiscriminator, self).__init__()
        self.ad_layer1 = nn.Linear(in_feature, 1024)
        self.ad_layer2 = nn.Linear(1024, 1024)
        self.ad_layer3 = nn.Linear(1024, 1)
        self.sigmoid = nn.Sigmoid()

        self.grl = GradientReverseModule(lambda step: aToBSheduler(step, 0.0, 1.0, gamma=10, max_iter=10000))

        self.main = nn.Sequential(
            self.ad_layer1,
            nn.BatchNorm1d(1024),
            nn.LeakyReLU(0.2, inplace=True),
            self.ad_layer2,
            nn.BatchNorm1d(1024),
            nn.LeakyReLU(0.2, inplace=True),
            self.ad_layer3,
            self.sigmoid
        )

    def forward(self, x):
        x = self.grl(x)
        for module in self.main.children():
            x = module(x)
        return x

class MultiDiscriminator(nn.Module):
    """多个域判别器网络，用于渐进式学习"""
    def __init__(self, n_discriminator, in_feature):
        super(MultiDiscriminator, self).__init__()
        self.n = n_discriminator

        def create_discriminator():
            return nn.Sequential(
                nn.Linear(in_feature, 256),
                nn.BatchNorm1d(256),
                nn.LeakyReLU(0.2, inplace=True),
                nn.Linear(256, 256),
                nn.BatchNorm1d(256),
                nn.LeakyReLU(0.2, inplace=True),
                nn.Linear(256, 1),
                nn.Sigmoid()
            )

        for i in range(n_discriminator):
            self.__setattr__(f'discriminator_{i:04d}', create_discriminator())

    def forward(self, x):
        outs = [self.__getattr__(f'discriminator_{i:04d}')(x) for i in range(self.n)]
        return torch.cat(outs, dim=-1)

class OneVsAllLabelGenerator(nn.Module):
    """逐类伪标签生成器"""
    def __init__(self, feature_dim, num_classes):
        super(OneVsAllLabelGenerator, self).__init__()
        self.num_classes = num_classes

        # 为每个类别创建一个二分类器
        self.binary_classifiers = nn.ModuleList([
            nn.Sequential(
                nn.Linear(feature_dim, 256),
                nn.BatchNorm1d(256),
                nn.ReLU(inplace=True),
                nn.Linear(256, 1),
                nn.Sigmoid()
            ) for _ in range(num_classes)
        ])

    def forward(self, x):
        # 对每个类别生成二分类结果
        binary_outputs = []
        for i in range(self.num_classes):
            binary_outputs.append(self.binary_classifiers[i](x))

        # 将所有二分类结果拼接成一个向量
        return torch.cat(binary_outputs, dim=1)

class TPTLN_GLC(nn.Module):
    """理论引导渐进式迁移学习网络 + GLC++"""
    def __init__(self, feature_extractor, num_classes, feature_dim=320, rho=0.3, local_k=4):
        super(TPTLN_GLC, self).__init__()
        self.feature_extractor = feature_extractor
        self.num_classes = num_classes
        self.feature_dim = feature_dim
        self.rho = rho  # 用于控制伪标签生成的参数
        self.local_k = local_k  # 局部对比学习的K值

        # 移除特征提取器的分类头
        if hasattr(self.feature_extractor, 'head'):
            self.feature_extractor.head = nn.Identity()

        # 分类器
        self.classifier = Classifier(feature_dim, num_classes)

        # 域判别器
        self.domain_discriminator = DomainDiscriminator(feature_dim)

        # 多判别器（渐进式学习）
        self.multi_discriminator = MultiDiscriminator(n_discriminator=5, in_feature=feature_dim)

        # 逐类伪标签生成器
        self.label_generator = OneVsAllLabelGenerator(feature_dim, num_classes)

        # 特征投影层（用于对比学习）
        self.projection = nn.Sequential(
            nn.Linear(feature_dim, feature_dim),
            nn.BatchNorm1d(feature_dim),
            nn.ReLU(inplace=True),
            nn.Linear(feature_dim, feature_dim)
        )

        # 初始化特征库和伪标签库
        self.feature_bank = None
        self.pseudo_label_bank = None
        self.is_feature_bank_initialized = False

    def forward(self, x, domain='source'):
        # 提取特征
        features = self.feature_extractor(x)

        # 分类
        cls_out = self.classifier(features)

        # 域对抗
        domain_pred = self.domain_discriminator(features)

        # 多判别器（渐进式学习）
        multi_domain_pred = self.multi_discriminator(features)

        # 逐类伪标签生成
        binary_preds = self.label_generator(features)

        # 特征投影（用于对比学习）
        proj_features = self.projection(features)

        return {
            'features': features,
            'logits': cls_out[1],  # softmax后的输出
            'domain_pred': domain_pred,
            'multi_domain_pred': multi_domain_pred,
            'binary_preds': binary_preds,
            'proj_features': proj_features,
            'embeddings': cls_out[0]  # 分类器输入的特征
        }

    def get_features(self, x):
        """仅提取特征，用于t-SNE可视化等"""
        return self.feature_extractor(x)

    @torch.no_grad()
    def generate_pseudo_labels(self, target_loader, device, epoch_idx=0):
        """生成目标域的伪标签"""
        self.eval()

        # 收集所有目标域样本的特征和预测
        all_features = []
        all_logits = []
        all_binary_preds = []
        all_indices = []

        # 适应不同的数据加载器格式
        for batch_idx, batch_data in enumerate(target_loader):
            # 检查数据加载器返回的格式
            if len(batch_data) == 3:  # (data, label, indices)
                data, _, indices = batch_data
            elif len(batch_data) == 2:  # (data, label)
                data, _ = batch_data
                # 创建批次索引
                indices = torch.arange(batch_idx * target_loader.batch_size,
                                      min((batch_idx + 1) * target_loader.batch_size, len(target_loader.dataset)))
            else:
                raise ValueError(f"Unexpected data format from loader: {len(batch_data)} items")

            data = data.to(device)
            outputs = self(data)

            all_features.append(outputs['features'].cpu())
            all_logits.append(outputs['logits'].cpu())
            all_binary_preds.append(outputs['binary_preds'].cpu())
            all_indices.append(indices)

        # 合并所有批次的结果
        all_features = torch.cat(all_features, dim=0)
        all_logits = torch.cat(all_logits, dim=0)
        all_binary_preds = torch.cat(all_binary_preds, dim=0)
        all_indices = torch.cat(all_indices, dim=0)

        # 归一化特征
        all_features = F.normalize(all_features, dim=1)

        # 初始化特征库（如果是第一次调用）
        if not self.is_feature_bank_initialized:
            # 创建足够大的特征库，以容纳所有可能的样本
            dataset_size = len(target_loader.dataset)
            self.feature_bank = torch.zeros((dataset_size, all_features.shape[1]))
            self.pseudo_label_bank = torch.zeros(dataset_size, self.num_classes)
            self.is_feature_bank_initialized = True

        # 更新特征库 - 使用索引更新对应位置的特征
        for i, idx in enumerate(all_indices):
            self.feature_bank[idx] = all_features[i]

        # 全局聚类 - 使用One-vs-All策略
        data_num = all_features.shape[0]
        pos_topk_num = int(data_num / self.num_classes / 3)  # 每个类别选择的高置信度样本数量

        # 对每个类别进行排序，选择高置信度样本
        sorted_binary_preds, sorted_indices = torch.sort(all_binary_preds, dim=0, descending=True)

        # 初始化伪标签
        pseudo_labels = torch.zeros(data_num, self.num_classes)

        # 对每个类别进行处理
        for cls_idx in range(self.num_classes):
            # 获取当前类别的高置信度样本索引
            pos_indices = sorted_indices[:pos_topk_num, cls_idx]
            pos_features = all_features[pos_indices]

            # 计算类别原型（中心）
            class_prototype = pos_features.mean(dim=0, keepdim=True)
            class_prototype = F.normalize(class_prototype, dim=1)

            # 计算所有样本与类别原型的相似度
            similarity = torch.mm(all_features, class_prototype.t()).squeeze()

            # 使用相似度作为该类别的伪标签概率
            pseudo_labels[:, cls_idx] = similarity * (1.0 - self.rho) + self.rho

        # 局部聚类 - 使用Mini-batch KMeans进一步细化
        # 对每个类别的高置信度样本进行局部聚类
        for cls_idx in range(self.num_classes):
            # 获取当前类别的高置信度样本
            high_conf_mask = (pseudo_labels[:, cls_idx] > 0.7)  # 阈值可调整
            if high_conf_mask.sum() > 50:  # 确保有足够的样本进行聚类
                high_conf_features = all_features[high_conf_mask].numpy()

                # 使用Mini-batch KMeans进行局部聚类
                n_clusters = min(5, high_conf_mask.sum() // 10)  # 根据样本数量确定聚类数
                if n_clusters > 1:
                    # 明确指定n_init参数，避免警告
                    kmeans = MiniBatchKMeans(n_clusters=n_clusters, random_state=0, batch_size=100, n_init=3)
                    cluster_labels = kmeans.fit_predict(high_conf_features)

                    # 计算每个聚类的中心
                    cluster_centers = torch.tensor(kmeans.cluster_centers_, dtype=torch.float32)

                    # 更新高置信度样本的伪标签
                    for i, idx in enumerate(torch.where(high_conf_mask)[0]):
                        cluster_idx = cluster_labels[i]
                        # 计算样本与其所属聚类中心的相似度
                        sample_feature = all_features[idx].unsqueeze(0)
                        center_feature = cluster_centers[cluster_idx].unsqueeze(0)
                        similarity = F.cosine_similarity(sample_feature, center_feature).item()

                        # 根据相似度调整伪标签
                        if similarity > 0.8:  # 高相似度，保留伪标签
                            pass
                        else:  # 低相似度，降低伪标签置信度（可能是噪声）
                            pseudo_labels[idx, cls_idx] *= 0.5

        # 确定最终的伪标签（硬标签）
        hard_pseudo_labels = torch.zeros_like(pseudo_labels)
        max_values, max_indices = torch.max(pseudo_labels, dim=1)

        # 只为高置信度样本分配类别，低置信度样本视为未知类别
        known_mask = (max_values > 0.7)  # 阈值可调整
        hard_pseudo_labels[known_mask, max_indices[known_mask]] = 1.0

        # 更新伪标签库
        for i, idx in enumerate(all_indices):
            self.pseudo_label_bank[idx] = hard_pseudo_labels[i]

        # 返回已知类别和未知类别的掩码
        known_indices = torch.where(known_mask)[0]
        unknown_indices = torch.where(~known_mask)[0]

        return {
            'pseudo_labels': hard_pseudo_labels,
            'known_indices': known_indices,
            'unknown_indices': unknown_indices,
            'all_indices': all_indices,
            'all_features': all_features
        }

    def contrastive_loss(self, features, pseudo_labels, known_indices, temperature=0.07):
        """计算局部对比损失"""
        if len(known_indices) < 2:
            return torch.tensor(0.0).to(features.device)

        # 只使用已知类别的样本计算对比损失
        features = features[known_indices]
        pseudo_labels = pseudo_labels[known_indices]

        # 归一化特征
        features = F.normalize(features, dim=1)

        # 计算特征之间的相似度矩阵
        similarity_matrix = torch.matmul(features, features.t()) / temperature

        # 创建标签矩阵，1表示正对，0表示负对
        pseudo_labels_argmax = torch.argmax(pseudo_labels, dim=1)
        label_matrix = torch.eq(pseudo_labels_argmax.unsqueeze(1), pseudo_labels_argmax.unsqueeze(0)).float()

        # 移除对角线（自身与自身的相似度）
        mask = torch.ones_like(similarity_matrix) - torch.eye(similarity_matrix.size(0), device=similarity_matrix.device)
        similarity_matrix = similarity_matrix * mask
        label_matrix = label_matrix * mask

        # 正对和负对的掩码
        positives = label_matrix
        negatives = 1.0 - positives

        # 计算对比损失
        neg_similarity = torch.exp(similarity_matrix) * negatives
        neg_similarity = neg_similarity.sum(dim=1, keepdim=True)

        pos_similarity = torch.exp(similarity_matrix) * positives
        pos_similarity = pos_similarity.sum(dim=1, keepdim=True)

        loss = -torch.log(pos_similarity / (pos_similarity + neg_similarity + 1e-8))

        # 只计算有正对的样本的损失
        valid_mask = (positives.sum(dim=1) > 0).float()
        loss = (loss * valid_mask).sum() / (valid_mask.sum() + 1e-8)

        return loss

def create_TPTLN_GLC_model(model_name, num_classes, feature_extractor):
    """
    创建TPTLN-GLC++模型

    Args:
        model_name: 模型名称
        num_classes: 分类类别数
        feature_extractor: 特征提取器

    Returns:
        TPTLN-GLC++模型实例
    """
    # 获取特征维度，对于convnextv2_atto是320
    feature_dim = 320

    model = TPTLN_GLC(feature_extractor, num_classes, feature_dim)

    # 打印创建的模型信息
    print(f"成功创建TPTLN-GLC++模型，特征提取器基于ConvNeXtV2 Atto，特征维度: {feature_dim}")
    print("注意：特征提取器的预训练权重已通过ConvNeXtV2_atto函数加载")

    return model
