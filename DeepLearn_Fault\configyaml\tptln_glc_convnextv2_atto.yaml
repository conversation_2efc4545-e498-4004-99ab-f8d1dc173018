# Theory-guided Progressive Transfer Learning Network with GLC++ Configuration - Using ConvNeXtV2 Atto

# Model settings
model_name: tptln_glc_convnextv2_atto

# Feature extractor pretrained settings
pretrained: true
pretrained_path: checkpoint/convnextv2_atto/convnextv2_atto_1k_224_ema.pt

# Training settings
batch_size: 32
n_epoch: 60
early_stop: 30

# Optimizer settings
lr: 0.00005
momentum: 0.9
weight_decay: 1e-5

# Learning rate scheduler settings
lr_gamma: 0.0003
lr_decay: 0.75
lr_scheduler: true
num_workers: 20

# Confusion matrix usage
confmtx: 1

# t-SNE visualization
t_sne: 1

# Transfer learning settings
transfer_loss_weight: 2.0
domain_adaptation_weight: 1.0
progressive_steps: 5
source_weight: 2.0
target_weight: 1.0

# GLC++ related parameters
rho: 0.3
local_k: 4
contrastive_weight: 1.0
pseudo_label_update_interval: 1
temperature: 0.07
