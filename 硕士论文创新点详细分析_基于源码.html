<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>硕士论文创新点详细分析：基于改进ConvNeXt V2和TPTLN-GLC++的故障诊断网络</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            border-bottom: 3px solid #3498db;
            padding-bottom: 15px;
            margin-bottom: 30px;
            font-size: 2.5em;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        h2 {
            color: #34495e;
            border-left: 5px solid #e74c3c;
            padding: 15px 20px;
            margin-top: 40px;
            font-size: 1.8em;
            background: linear-gradient(90deg, #fdf2f2, #ffffff);
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }
        .innovation-detail {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px;
            margin: 25px 0;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
        }
        .innovation-content {
            background-color: white;
            color: #333;
            padding: 25px;
            border-radius: 10px;
            margin-top: 15px;
        }
        .comparison-container {
            display: flex;
            gap: 20px;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        .before-after {
            flex: 1;
            min-width: 350px;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .before {
            background: linear-gradient(135deg, #ffebee, #ffcdd2);
            border: 2px solid #f44336;
        }
        .after {
            background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
            border: 2px solid #4caf50;
        }
        .code-block {
            background-color: #1e1e1e;
            color: #d4d4d4;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Consolas', 'Monaco', monospace;
            overflow-x: auto;
            margin: 15px 0;
            border-left: 4px solid #007acc;
            font-size: 14px;
        }
        .method-steps {
            background: linear-gradient(135deg, #fff3e0, #ffe0b2);
            border-left: 5px solid #ff9800;
            padding: 20px;
            margin: 15px 0;
            border-radius: 8px;
        }
        .innovation-number {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 1.5em;
            margin-right: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .improvement-highlight {
            background: linear-gradient(45deg, #56ab2f, #a8e6cf);
            color: white;
            padding: 8px 12px;
            border-radius: 20px;
            font-weight: bold;
            display: inline-block;
            margin: 2px;
        }
        .brief-section {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            padding: 20px;
            border-radius: 10px;
            margin: 15px 0;
            border-left: 4px solid #6c757d;
        }
        .flow-diagram {
            background: linear-gradient(90deg, #74b9ff, #0984e3);
            color: white;
            padding: 25px;
            border-radius: 15px;
            margin: 20px 0;
            text-align: center;
        }
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-radius: 10px;
            overflow: hidden;
        }
        .comparison-table th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            text-align: center;
            font-weight: bold;
        }
        .comparison-table td {
            padding: 12px;
            border: 1px solid #ddd;
            text-align: center;
            background: white;
        }
        .comparison-table tr:nth-child(even) td {
            background-color: #f8f9fa;
        }
        .keyword {
            color: #569cd6;
            font-weight: bold;
        }
        .comment {
            color: #6a9955;
        }
        .string {
            color: #ce9178;
        }
        .function {
            color: #dcdcaa;
        }
        .architecture-box {
            background: white;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            font-family: monospace;
            border: 2px dashed;
            text-align: center;
        }
        .step-box {
            background: #f8f9fa;
            padding: 8px;
            border-radius: 4px;
            margin: 5px 0;
            border-left: 3px solid;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>硕士论文创新点详细分析：基于改进ConvNeXt V2和TPTLN-GLC++的故障诊断网络</h1>

        <!-- 简要概述 -->
        <div class="brief-section">
            <h3>📋 基于源码的创新点分析</h3>
            <p><strong>原始ConvNeXt V2源码：</strong>DeepLearn_Fault/ConvNeXt-V2-main/models/convnextv2.py</p>
            <p><strong>原始TPTLN源码：</strong>DeepLearn_Fault/Theory-guided-Progressive-Transfer-Learning-Network-main/Model.py</p>
            <p><strong>改进后实现：</strong>DeepLearn_Fault/models/ConvNeXtV2.py 和 DeepLearn_Fault/models/TPTLN_GLC.py</p>
        </div>

        <!-- 创新点1：小波变换卷积层 -->
        <h2><span class="innovation-number">1</span>创新点一：小波变换卷积层（WTConv2d）</h2>

        <div class="innovation-detail">
            <h3>🔬 创新归属：提出</h3>
            <h3>💡 创新思路：将原始ConvNeXt V2的标准深度卷积替换为小波变换卷积</h3>

            <div class="innovation-content">
                <h4>📊 源码架构对比</h4>
                <div class="comparison-container">
                    <div class="before-after before">
                        <h4>🔴 原始ConvNeXt V2 Block</h4>
                        <div class="architecture-box" style="border-color: #f44336;">
                            <div style="font-weight: bold; margin-bottom: 10px;">Input (N, C, H, W)</div>
                            <div style="margin: 5px 0;">↓</div>
                            <div class="step-box" style="border-color: #f44336; background: #ffebee;">
                                <strong>Depthwise Conv 7×7</strong><br>
                                <small>(groups=C, 单一尺度)</small>
                            </div>
                            <div style="margin: 5px 0;">↓</div>
                            <div class="step-box" style="border-color: #f44336; background: #ffebee;">LayerNorm</div>
                            <div style="margin: 5px 0;">↓</div>
                            <div class="step-box" style="border-color: #f44336; background: #ffebee;">Linear (C → 4C)</div>
                            <div style="margin: 5px 0;">↓</div>
                            <div class="step-box" style="border-color: #f44336; background: #ffebee;">GELU + GRN</div>
                            <div style="margin: 5px 0;">↓</div>
                            <div class="step-box" style="border-color: #f44336; background: #ffebee;">Linear (4C → C)</div>
                            <div style="margin: 5px 0;">↓</div>
                            <div style="font-weight: bold; margin-top: 10px;">Output (N, C, H, W)</div>
                        </div>
                        <p><strong>局限性：</strong></p>
                        <ul>
                            <li>只能提取空域特征</li>
                            <li>缺乏多尺度分析能力</li>
                            <li>对频域信息不敏感</li>
                        </ul>
                    </div>

                    <div class="before-after after">
                        <h4>🟢 改进后WTConv2d Block</h4>
                        <div class="architecture-box" style="border-color: #4caf50;">
                            <div style="font-weight: bold; margin-bottom: 10px;">Input (N, C, H, W)</div>
                            <div style="margin: 5px 0;">↓</div>
                            <div class="step-box" style="border-color: #4caf50; background: #e8f5e8;">
                                <strong>Wavelet Transform</strong><br>
                                <small>(LL, LH, HL, HH子带)</small>
                            </div>
                            <div style="margin: 5px 0;">↓</div>
                            <div class="step-box" style="border-color: #4caf50; background: #e8f5e8;">
                                <strong>Multi-scale Convolution</strong><br>
                                <small>(4个子带并行处理)</small>
                            </div>
                            <div style="margin: 5px 0;">↓</div>
                            <div class="step-box" style="border-color: #4caf50; background: #e8f5e8;">Inverse Wavelet Transform</div>
                            <div style="margin: 5px 0;">↓</div>
                            <div class="step-box" style="border-color: #4caf50; background: #e8f5e8;">Base Conv + Scale</div>
                            <div style="margin: 5px 0;">↓</div>
                            <div style="font-weight: bold; margin-top: 10px;">Output (N, C, H, W)</div>
                        </div>
                        <p><strong>优势：</strong></p>
                        <ul>
                            <li>时频域联合特征提取</li>
                            <li>多分辨率分析能力</li>
                            <li>自适应小波滤波器</li>
                        </ul>
                    </div>
                </div>

                <h4>🔧 核心代码对比</h4>
                <div class="method-steps">
                    <h5>原始ConvNeXt V2 Block实现</h5>
                    <div class="code-block">
<span class="keyword">class</span> <span class="function">Block</span>(nn.Module):
    <span class="keyword">def</span> <span class="function">__init__</span>(self, dim, drop_path=0.):
        super().__init__()
        <span class="comment"># 标准深度卷积 - 单一尺度</span>
        self.dwconv = nn.Conv2d(dim, dim, kernel_size=7, padding=3, groups=dim)
        self.norm = LayerNorm(dim, eps=1e-6)
        self.pwconv1 = nn.Linear(dim, 4 * dim)
        self.act = nn.GELU()
        self.grn = GRN(4 * dim)
        self.pwconv2 = nn.Linear(4 * dim, dim)

    <span class="keyword">def</span> <span class="function">forward</span>(self, x):
        input = x
        x = self.dwconv(x)  <span class="comment"># 单一尺度深度卷积</span>
        x = x.permute(0, 2, 3, 1)
        x = self.norm(x)
        x = self.pwconv1(x)
        x = self.act(x)
        x = self.grn(x)
        x = self.pwconv2(x)
        x = x.permute(0, 3, 1, 2)
        x = input + self.drop_path(x)
        <span class="keyword">return</span> x
                    </div>
                </div>

                <div class="method-steps">
                    <h5>改进后WTConv2d实现</h5>
                    <div class="code-block">
<span class="keyword">class</span> <span class="function">WTConv2d</span>(nn.Module):
    <span class="keyword">def</span> <span class="function">__init__</span>(self, in_channels, out_channels, kernel_size=5,
                 stride=1, bias=True, wt_levels=1, wt_type=<span class="string">'db1'</span>):
        super(WTConv2d, self).__init__()

        <span class="comment"># 创建小波滤波器（核心创新）</span>
        self.wt_filter, self.iwt_filter = create_wavelet_filter(
            wt_type, in_channels, in_channels, torch.float)
        self.wt_filter = nn.Parameter(self.wt_filter, requires_grad=False)

        <span class="comment"># 基础卷积层（处理低频信息）</span>
        self.base_conv = nn.Conv2d(in_channels, in_channels,
                                  kernel_size, padding=<span class="string">'same'</span>,
                                  stride=1, groups=in_channels, bias=bias)

        <span class="comment"># 小波卷积层（处理多尺度信息）</span>
        self.wavelet_convs = nn.ModuleList([
            nn.Conv2d(in_channels*4, in_channels*4, kernel_size,
                     padding=<span class="string">'same'</span>, stride=1, groups=in_channels*4, bias=False)
            <span class="keyword">for</span> _ <span class="keyword">in</span> range(self.wt_levels)
        ])

    <span class="keyword">def</span> <span class="function">forward</span>(self, x):
        <span class="comment"># 多尺度小波变换处理</span>
        x_ll_in_levels = []
        x_h_in_levels = []
        curr_x_ll = x

        <span class="keyword">for</span> i <span class="keyword">in</span> range(self.wt_levels):
            <span class="comment"># 小波变换分解</span>
            curr_x = wavelet_transform(curr_x_ll, self.wt_filter)
            curr_x_ll = curr_x[:,:,0,:,:]

            <span class="comment"># 多尺度卷积处理</span>
            shape_x = curr_x.shape
            curr_x_tag = curr_x.reshape(shape_x[0], shape_x[1] * 4, shape_x[3], shape_x[4])
            curr_x_tag = self.wavelet_scale[i](self.wavelet_convs[i](curr_x_tag))
            curr_x_tag = curr_x_tag.reshape(shape_x)

            x_ll_in_levels.append(curr_x_tag[:,:,0,:,:])
            x_h_in_levels.append(curr_x_tag[:,:,1:4,:,:])

        <span class="comment"># 逆小波变换重构</span>
        next_x_ll = 0
        <span class="keyword">for</span> i <span class="keyword">in</span> range(self.wt_levels-1, -1, -1):
            curr_x_ll = x_ll_in_levels.pop()
            curr_x_h = x_h_in_levels.pop()
            curr_x_ll = curr_x_ll + next_x_ll
            curr_x = torch.cat([curr_x_ll.unsqueeze(2), curr_x_h], dim=2)
            next_x_ll = inverse_wavelet_transform(curr_x, self.iwt_filter)

        <span class="comment"># 基础卷积处理和残差连接</span>
        x_base = self.base_scale(self.base_conv(x))
        x = x_base + next_x_ll

        <span class="keyword">return</span> x
                    </div>
                </div>

                <h4>🎯 小波变换的创新原理</h4>
                <div class="flow-diagram">
                    <h5>小波变换多尺度分解原理</h5>
                    <div style="text-align: left; font-family: monospace;">
                        <strong>传统卷积：</strong> 单一尺度 → 空域特征<br>
                        <strong>小波卷积：</strong> 多尺度分解 → 时频域特征<br><br>

                        <strong>四个子带分解：</strong><br>
                        • <strong>LL子带：</strong>低频-低频（主要结构信息）<br>
                        • <strong>LH子带：</strong>低频-高频（水平边缘信息）<br>
                        • <strong>HL子带：</strong>高频-低频（垂直边缘信息）<br>
                        • <strong>HH子带：</strong>高频-高频（对角线细节信息）<br><br>

                        <strong>创新优势：</strong><br>
                        ✓ 同时提取多个尺度的特征<br>
                        ✓ 保留频域信息用于故障特征识别<br>
                        ✓ 自适应学习最优小波基函数
                    </div>
                </div>
            </div>
        </div>

        <!-- 创新点2：EMA注意力机制 -->
        <h2><span class="innovation-number">2</span>创新点二：EMA注意力机制集成</h2>

        <div class="innovation-detail">
            <h3>🔬 创新归属：引入并改进</h3>
            <h3>💡 创新思路：在ConvNeXt V2的Block中集成EMA注意力机制，提升特征选择性</h3>

            <div class="innovation-content">
                <h4>📊 EMA注意力机制设计</h4>
                <div class="comparison-container">
                    <div class="before-after before">
                        <h4>🔴 原始ConvNeXt V2（无注意力）</h4>
                        <div class="architecture-box" style="border-color: #f44336;">
                            <div style="font-weight: bold; margin-bottom: 10px;">Input Features (N, C, H, W)</div>
                            <div style="margin: 5px 0;">↓</div>
                            <div class="step-box" style="border-color: #f44336; background: #ffebee;">Pointwise Conv (C → 4C)</div>
                            <div style="margin: 5px 0;">↓</div>
                            <div class="step-box" style="border-color: #f44336; background: #ffebee;">GELU Activation</div>
                            <div style="margin: 5px 0;">↓</div>
                            <div class="step-box" style="border-color: #f44336; background: #ffebee;">GRN (Global Response Norm)</div>
                            <div style="margin: 5px 0;">↓</div>
                            <div class="step-box" style="border-color: #f44336; background: #ffebee;">Pointwise Conv (4C → C)</div>
                            <div style="margin: 5px 0;">↓</div>
                            <div style="font-weight: bold; margin-top: 10px;">Output (N, C, H, W)</div>
                        </div>
                        <p><strong>问题：</strong></p>
                        <ul>
                            <li>缺乏空间注意力机制</li>
                            <li>通道间关系建模不足</li>
                            <li>特征选择性较弱</li>
                        </ul>
                    </div>

                    <div class="before-after after">
                        <h4>🟢 集成EMA注意力后</h4>
                        <div class="architecture-box" style="border-color: #4caf50;">
                            <div style="font-weight: bold; margin-bottom: 10px;">Input Features (N, C, H, W)</div>
                            <div style="margin: 5px 0;">↓</div>
                            <div class="step-box" style="border-color: #4caf50; background: #e8f5e8;">Pointwise Conv (C → 4C)</div>
                            <div style="margin: 5px 0;">↓</div>
                            <div class="step-box" style="border-color: #4caf50; background: #e8f5e8;">GELU Activation</div>
                            <div style="margin: 5px 0;">↓</div>
                            <div class="step-box" style="border-color: #4caf50; background: #e8f5e8;">GRN (Global Response Norm)</div>
                            <div style="margin: 5px 0;">↓</div>
                            <div class="step-box" style="border-color: #4caf50; background: #a8e6cf;">
                                <strong>EMA Attention Module</strong><br>
                                <small>├─ Adaptive Pooling (H×1, 1×W)<br>
                                ├─ Group Normalization<br>
                                ├─ 1×1 Conv + Sigmoid<br>
                                └─ Feature Recalibration</small>
                            </div>
                            <div style="margin: 5px 0;">↓</div>
                            <div class="step-box" style="border-color: #4caf50; background: #e8f5e8;">Pointwise Conv (4C → C)</div>
                            <div style="margin: 5px 0;">↓</div>
                            <div style="font-weight: bold; margin-top: 10px;">Output (N, C, H, W)</div>
                        </div>
                        <p><strong>改进：</strong></p>
                        <ul>
                            <li>增强空间特征选择</li>
                            <li>提升通道间交互</li>
                            <li>自适应特征重标定</li>
                        </ul>
                    </div>
                </div>

                <h4>🔧 EMA注意力机制实现</h4>
                <div class="method-steps">
                    <h5>EMA注意力核心代码</h5>
                    <div class="code-block">
<span class="keyword">class</span> <span class="function">EMA</span>(nn.Module):
    <span class="keyword">def</span> <span class="function">__init__</span>(self, channels, c2=None, factor=32):
        super(EMA, self).__init__()
        self.groups = factor
        assert channels // self.groups > 0

        <span class="comment"># 多方向自适应池化</span>
        self.agp = nn.AdaptiveAvgPool2d((1, 1))      <span class="comment"># 全局池化</span>
        self.pool_h = nn.AdaptiveAvgPool2d((None, 1)) <span class="comment"># 水平池化</span>
        self.pool_w = nn.AdaptiveAvgPool2d((1, None)) <span class="comment"># 垂直池化</span>

        <span class="comment"># 分组归一化和卷积</span>
        self.gn = nn.GroupNorm(channels // self.groups, channels // self.groups)
        self.conv1x1 = nn.Conv2d(channels // self.groups, channels // self.groups,
                                kernel_size=1, stride=1, padding=0)
        self.conv3x3 = nn.Conv2d(channels // self.groups, channels // self.groups,
                                kernel_size=3, stride=1, padding=1)

    <span class="keyword">def</span> <span class="function">forward</span>(self, x):
        b, c, h, w = x.size()
        <span class="comment"># 分组处理，增强计算效率</span>
        group_x = x.reshape(b * self.groups, -1, h, w)

        <span class="comment"># 多方向池化获取空间信息</span>
        x_h = self.pool_h(group_x)                      <span class="comment"># (b*g, c//g, h, 1)</span>
        x_w = self.pool_w(group_x).permute(0, 1, 3, 2)  <span class="comment"># (b*g, c//g, w, 1)</span>

        <span class="comment"># 拼接并通过1×1卷积融合</span>
        hw = self.conv1x1(torch.cat([x_h, x_w], dim=2))
        x_h, x_w = torch.split(hw, [h, w], dim=2)

        <span class="comment"># 生成空间注意力权重</span>
        x1 = self.gn(group_x * x_h.sigmoid() * x_w.permute(0, 1, 3, 2).sigmoid())
        x2 = self.conv3x3(group_x)

        <span class="comment"># 通道注意力计算</span>
        x11 = self.softmax(self.agp(x1).reshape(b * self.groups, -1, 1).permute(0, 2, 1))
        x12 = x2.reshape(b * self.groups, c // self.groups, -1)
        x21 = self.softmax(self.agp(x2).reshape(b * self.groups, -1, 1).permute(0, 2, 1))
        x22 = x1.reshape(b * self.groups, c // self.groups, -1)

        <span class="comment"># 最终注意力权重</span>
        weights = (torch.matmul(x11, x12) + torch.matmul(x21, x22)).reshape(b * self.groups, 1, h, w)
        <span class="keyword">return</span> (group_x * weights.sigmoid()).reshape(b, c, h, w)
                    </div>
                </div>
            </div>
        </div>

        <!-- 创新点3：TPTLN-GLC++聚类策略 -->
        <h2><span class="innovation-number">3</span>创新点三：改进的TPTLN-GLC++聚类策略</h2>

        <div class="innovation-detail">
            <h3>🔬 创新归属：设计并改进</h3>
            <h3>💡 创新思路：结合全局聚类和局部聚类，提升无监督域适应中的伪标签质量</h3>

            <div class="innovation-content">
                <h4>📊 原始TPTLN与改进TPTLN-GLC++对比</h4>
                <div class="comparison-container">
                    <div class="before-after before">
                        <h4>🔴 原始TPTLN方法</h4>
                        <div class="architecture-box" style="border-color: #f44336;">
                            <div style="font-weight: bold; margin-bottom: 10px;">目标域特征提取</div>
                            <div style="margin: 5px 0;">↓</div>
                            <div class="step-box" style="border-color: #f44336; background: #ffebee;">
                                简单K-means聚类
                            </div>
                            <div style="margin: 5px 0;">↓</div>
                            <div class="step-box" style="border-color: #f44336; background: #ffebee;">
                                伪标签生成<br>
                                <small>(硬标签分配)</small>
                            </div>
                            <div style="margin: 5px 0;">↓</div>
                            <div class="step-box" style="border-color: #f44336; background: #ffebee;">
                                域对抗训练<br>
                                <small>(单一判别器)</small>
                            </div>
                            <div style="margin: 5px 0;">↓</div>
                            <div style="font-weight: bold; margin-top: 10px;">分类器更新</div>
                        </div>
                        <p><strong>局限性：</strong></p>
                        <ul>
                            <li>聚类策略单一</li>
                            <li>伪标签质量不高</li>
                            <li>缺乏渐进式学习</li>
                            <li>域对抗训练不稳定</li>
                        </ul>
                    </div>

                    <div class="before-after after">
                        <h4>🟢 改进TPTLN-GLC++</h4>
                        <div class="architecture-box" style="border-color: #4caf50;">
                            <div style="font-weight: bold; margin-bottom: 10px;">改进ConvNeXt V2特征提取</div>
                            <div style="margin: 5px 0;">↓</div>
                            <div class="step-box" style="border-color: #4caf50; background: #a8e6cf;">
                                <strong>One-vs-All全局聚类</strong><br>
                                <small>(每类别二分类器)</small>
                            </div>
                            <div style="margin: 5px 0;">↓</div>
                            <div class="step-box" style="border-color: #4caf50; background: #a8e6cf;">
                                <strong>Mini-batch KMeans局部聚类</strong><br>
                                <small>(高置信度样本细化)</small>
                            </div>
                            <div style="margin: 5px 0;">↓</div>
                            <div class="step-box" style="border-color: #4caf50; background: #a8e6cf;">
                                <strong>对比学习增强</strong><br>
                                <small>(特征判别性提升)</small>
                            </div>
                            <div style="margin: 5px 0;">↓</div>
                            <div class="step-box" style="border-color: #4caf50; background: #a8e6cf;">
                                <strong>渐进式训练</strong><br>
                                <small>(Distract + Attract阶段)</small>
                            </div>
                            <div style="margin: 5px 0;">↓</div>
                            <div class="step-box" style="border-color: #4caf50; background: #a8e6cf;">
                                <strong>多判别器协同</strong><br>
                                <small>(5个并行判别器)</small>
                            </div>
                            <div style="margin: 5px 0;">↓</div>
                            <div style="font-weight: bold; margin-top: 10px;">高质量故障诊断结果</div>
                        </div>
                        <p><strong>改进优势：</strong></p>
                        <ul>
                            <li>多层次聚类策略</li>
                            <li>高质量伪标签生成</li>
                            <li>理论引导训练</li>
                            <li>稳定的域对抗</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- 创新点4：渐进式训练策略 -->
        <h2><span class="innovation-number">4</span>创新点四：理论引导的渐进式训练策略</h2>

        <div class="innovation-detail">
            <h3>🔬 创新归属：提出</h3>
            <h3>💡 创新思路：将训练过程分为Distract和Attract两个阶段，逐步提升域适应效果</h3>

            <div class="innovation-content">
                <h4>📊 传统训练与渐进式训练对比</h4>
                <div class="comparison-container">
                    <div class="before-after before">
                        <h4>🔴 传统端到端训练</h4>
                        <div class="architecture-box" style="border-color: #f44336;">
                            <div style="font-weight: bold; margin-bottom: 10px;">初始化网络参数</div>
                            <div style="margin: 5px 0;">↓</div>
                            <div class="step-box" style="border-color: #f44336; background: #ffebee;">
                                同时进行：<br>
                                ├─ 源域分类训练<br>
                                ├─ 域对抗训练<br>
                                └─ 目标域伪标签学习
                            </div>
                            <div style="margin: 5px 0;">↓</div>
                            <div class="step-box" style="border-color: #f44336; background: #ffebee;">联合优化所有损失</div>
                            <div style="margin: 5px 0;">↓</div>
                            <div style="font-weight: bold; margin-top: 10px; color: #f44336;">容易陷入局部最优</div>
                        </div>
                        <p><strong>问题：</strong></p>
                        <ul>
                            <li>多任务冲突严重</li>
                            <li>训练不稳定</li>
                            <li>容易过拟合</li>
                            <li>收敛速度慢</li>
                        </ul>
                    </div>

                    <div class="before-after after">
                        <h4>🟢 渐进式两阶段训练</h4>
                        <div class="architecture-box" style="border-color: #4caf50;">
                            <div class="step-box" style="border-color: #4caf50; background: #a8e6cf;">
                                <strong>阶段1：Distract（分离）</strong><br>
                                ├─ 分离已知/未知类别<br>
                                ├─ 建立可靠伪标签<br>
                                └─ 初步域对抗训练
                            </div>
                            <div style="margin: 5px 0;">↓</div>
                            <div class="step-box" style="border-color: #4caf50; background: #a8e6cf;">
                                <strong>阶段2：Attract（吸引）</strong><br>
                                ├─ 对齐已知类别特征<br>
                                ├─ 精细化伪标签<br>
                                └─ 强化域对抗训练
                            </div>
                            <div style="margin: 5px 0;">↓</div>
                            <div class="step-box" style="border-color: #4caf50; background: #a8e6cf;">
                                <strong>多判别器协同</strong><br>
                                5个并行判别器稳定训练
                            </div>
                            <div style="margin: 5px 0;">↓</div>
                            <div style="font-weight: bold; margin-top: 10px; color: #4caf50;">稳定收敛到全局最优</div>
                        </div>
                        <p><strong>优势：</strong></p>
                        <ul>
                            <li>理论指导训练</li>
                            <li>阶段性目标明确</li>
                            <li>训练过程稳定</li>
                            <li>收敛效果更好</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- 创新点总结 -->
        <h2><span class="innovation-number">📊</span>创新点综合总结</h2>

        <div class="innovation-detail">
            <h3>🎯 四大创新点协同效应</h3>

            <div class="innovation-content">
                <div class="flow-diagram">
                    <h4>创新机制协同工作流程</h4>
                    <div style="text-align: center; font-family: monospace; font-size: 16px;">
                        <div class="improvement-highlight">小波变换卷积层</div><br>
                        ↓ (提供多尺度特征)<br>
                        <div class="improvement-highlight">EMA注意力机制</div><br>
                        ↓ (增强特征选择性)<br>
                        <div class="improvement-highlight">GLC++聚类策略</div><br>
                        ↓ (生成高质量伪标签)<br>
                        <div class="improvement-highlight">渐进式训练策略</div><br>
                        ↓ (稳定优化过程)<br>
                        <strong>🎯 最终故障诊断结果</strong>
                    </div>
                </div>

                <h4>🏆 创新点贡献度量化分析</h4>
                <table class="comparison-table">
                    <tr>
                        <th>创新点</th>
                        <th>创新归属</th>
                        <th>技术难度</th>
                        <th>理论贡献</th>
                        <th>实用价值</th>
                        <th>核心优势</th>
                    </tr>
                    <tr>
                        <td><strong>小波变换卷积层</strong></td>
                        <td>提出</td>
                        <td>⭐⭐⭐⭐⭐</td>
                        <td>⭐⭐⭐⭐</td>
                        <td>⭐⭐⭐⭐⭐</td>
                        <td>时频域联合特征提取</td>
                    </tr>
                    <tr>
                        <td><strong>EMA注意力集成</strong></td>
                        <td>引入改进</td>
                        <td>⭐⭐⭐</td>
                        <td>⭐⭐⭐</td>
                        <td>⭐⭐⭐⭐</td>
                        <td>空间-通道联合注意力</td>
                    </tr>
                    <tr>
                        <td><strong>GLC++聚类策略</strong></td>
                        <td>设计改进</td>
                        <td>⭐⭐⭐⭐⭐</td>
                        <td>⭐⭐⭐⭐⭐</td>
                        <td>⭐⭐⭐⭐⭐</td>
                        <td>全局-局部联合聚类</td>
                    </tr>
                    <tr>
                        <td><strong>渐进式训练策略</strong></td>
                        <td>提出</td>
                        <td>⭐⭐⭐⭐</td>
                        <td>⭐⭐⭐⭐⭐</td>
                        <td>⭐⭐⭐⭐⭐</td>
                        <td>理论引导两阶段训练</td>
                    </tr>
                </table>

                <h4>📝 论文写作重点建议</h4>
                <div class="brief-section">
                    <h5>创新点表述策略：</h5>
                    <ul>
                        <li><strong>突出首创性：</strong>"首次将小波变换引入ConvNeXt V2架构"</li>
                        <li><strong>强调理论性：</strong>"提出理论引导的渐进式训练策略"</li>
                        <li><strong>体现系统性：</strong>"构建了完整的TPTLN-GLC++框架"</li>
                        <li><strong>展现实用性：</strong>"轻量化设计满足工业应用需求"</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
