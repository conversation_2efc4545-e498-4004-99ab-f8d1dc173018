<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>硕士论文创新点详细分析：基于改进ConvNeXt V2和TPTLN-GLC++的故障诊断网络</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            border-bottom: 3px solid #3498db;
            padding-bottom: 15px;
            margin-bottom: 30px;
            font-size: 2.5em;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        h2 {
            color: #34495e;
            border-left: 5px solid #e74c3c;
            padding: 15px 20px;
            margin-top: 40px;
            font-size: 1.8em;
            background: linear-gradient(90deg, #fdf2f2, #ffffff);
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }
        .innovation-detail {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px;
            margin: 25px 0;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
        }
        .innovation-content {
            background-color: white;
            color: #333;
            padding: 25px;
            border-radius: 10px;
            margin-top: 15px;
        }
        .comparison-container {
            display: flex;
            gap: 20px;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        .before-after {
            flex: 1;
            min-width: 350px;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .before {
            background: linear-gradient(135deg, #ffebee, #ffcdd2);
            border: 2px solid #f44336;
        }
        .after {
            background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
            border: 2px solid #4caf50;
        }
        .code-block {
            background-color: #1e1e1e;
            color: #d4d4d4;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Consolas', 'Monaco', monospace;
            overflow-x: auto;
            margin: 15px 0;
            border-left: 4px solid #007acc;
            font-size: 14px;
        }
        .method-steps {
            background: linear-gradient(135deg, #fff3e0, #ffe0b2);
            border-left: 5px solid #ff9800;
            padding: 20px;
            margin: 15px 0;
            border-radius: 8px;
        }
        .innovation-number {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 1.5em;
            margin-right: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .architecture-svg {
            width: 100%;
            max-width: 600px;
            height: 400px;
            margin: 20px auto;
            display: block;
            background: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .improvement-highlight {
            background: linear-gradient(45deg, #56ab2f, #a8e6cf);
            color: white;
            padding: 8px 12px;
            border-radius: 20px;
            font-weight: bold;
            display: inline-block;
            margin: 2px;
        }
        .brief-section {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            padding: 20px;
            border-radius: 10px;
            margin: 15px 0;
            border-left: 4px solid #6c757d;
        }
        .flow-diagram {
            background: linear-gradient(90deg, #74b9ff, #0984e3);
            color: white;
            padding: 25px;
            border-radius: 15px;
            margin: 20px 0;
            text-align: center;
        }
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-radius: 10px;
            overflow: hidden;
        }
        .comparison-table th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            text-align: center;
            font-weight: bold;
        }
        .comparison-table td {
            padding: 12px;
            border: 1px solid #ddd;
            text-align: center;
            background: white;
        }
        .comparison-table tr:nth-child(even) td {
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>硕士论文创新点详细分析：基于改进ConvNeXt V2和TPTLN-GLC++的故障诊断网络</h1>

        <!-- 简要概述 -->
        <div class="brief-section">
            <h3>📋 基于源码的创新点分析</h3>
            <p><strong>原始ConvNeXt V2源码：</strong>DeepLearn_Fault/ConvNeXt-V2-main/models/convnextv2.py</p>
            <p><strong>原始TPTLN源码：</strong>DeepLearn_Fault/Theory-guided-Progressive-Transfer-Learning-Network-main/Model.py</p>
            <p><strong>改进后实现：</strong>DeepLearn_Fault/models/ConvNeXtV2.py 和 DeepLearn_Fault/models/TPTLN_GLC.py</p>
        </div>

        <!-- 创新点1：小波变换卷积层 -->
        <h2><span class="innovation-number">1</span>创新点一：小波变换卷积层（WTConv2d）</h2>
        
        <div class="innovation-detail">
            <h3>🔬 创新归属：提出</h3>
            <h3>💡 创新思路：将原始ConvNeXt V2的标准深度卷积替换为小波变换卷积</h3>
            
            <div class="innovation-content">
                <h4>📊 源码对比分析</h4>
                <div class="comparison-container">
                    <div class="before-after before">
                        <h4>🔴 原始ConvNeXt V2 Block</h4>
                        <svg class="architecture-svg" viewBox="0 0 400 300">
                            <rect x="50" y="20" width="300" height="40" fill="#ffcdd2" stroke="#f44336" stroke-width="2" rx="5"/>
                            <text x="200" y="45" text-anchor="middle" font-size="14" fill="#333">Input (N, C, H, W)</text>
                            
                            <line x1="200" y1="60" x2="200" y2="80" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
                            
                            <rect x="50" y="80" width="300" height="40" fill="#ffebee" stroke="#f44336" stroke-width="2" rx="5"/>
                            <text x="200" y="105" text-anchor="middle" font-size="12" fill="#333">nn.Conv2d(dim, dim, kernel_size=7, groups=dim)</text>
                            
                            <line x1="200" y1="120" x2="200" y2="140" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
                            
                            <rect x="50" y="140" width="300" height="40" fill="#ffebee" stroke="#f44336" stroke-width="2" rx="5"/>
                            <text x="200" y="165" text-anchor="middle" font-size="12" fill="#333">LayerNorm + Linear + GELU + GRN</text>
                            
                            <line x1="200" y1="180" x2="200" y2="200" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
                            
                            <rect x="50" y="200" width="300" height="40" fill="#ffcdd2" stroke="#f44336" stroke-width="2" rx="5"/>
                            <text x="200" y="225" text-anchor="middle" font-size="14" fill="#333">Output (N, C, H, W)</text>
                            
                            <defs>
                                <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                                    <polygon points="0 0, 10 3.5, 0 7" fill="#333"/>
                                </marker>
                            </defs>
                        </svg>
                        <p><strong>局限性：</strong></p>
                        <ul>
                            <li>只能提取空域特征</li>
                            <li>缺乏多尺度分析能力</li>
                            <li>对频域信息不敏感</li>
                        </ul>
                    </div>
                    
                    <div class="before-after after">
                        <h4>🟢 改进后WTConv2d Block</h4>
                        <svg class="architecture-svg" viewBox="0 0 400 350">
                            <rect x="50" y="20" width="300" height="30" fill="#c8e6c9" stroke="#4caf50" stroke-width="2" rx="5"/>
                            <text x="200" y="40" text-anchor="middle" font-size="14" fill="#333">Input (N, C, H, W)</text>
                            
                            <line x1="200" y1="50" x2="200" y2="70" stroke="#333" stroke-width="2" marker-end="url(#arrowhead2)"/>
                            
                            <rect x="50" y="70" width="300" height="30" fill="#a8e6cf" stroke="#4caf50" stroke-width="2" rx="5"/>
                            <text x="200" y="90" text-anchor="middle" font-size="12" fill="#333">Wavelet Transform (LL, LH, HL, HH)</text>
                            
                            <line x1="200" y1="100" x2="200" y2="120" stroke="#333" stroke-width="2" marker-end="url(#arrowhead2)"/>
                            
                            <rect x="50" y="120" width="300" height="30" fill="#a8e6cf" stroke="#4caf50" stroke-width="2" rx="5"/>
                            <text x="200" y="140" text-anchor="middle" font-size="12" fill="#333">Multi-scale Convolution (4 subbands)</text>
                            
                            <line x1="200" y1="150" x2="200" y2="170" stroke="#333" stroke-width="2" marker-end="url(#arrowhead2)"/>
                            
                            <rect x="50" y="170" width="300" height="30" fill="#a8e6cf" stroke="#4caf50" stroke-width="2" rx="5"/>
                            <text x="200" y="190" text-anchor="middle" font-size="12" fill="#333">Inverse Wavelet Transform</text>
                            
                            <line x1="200" y1="200" x2="200" y2="220" stroke="#333" stroke-width="2" marker-end="url(#arrowhead2)"/>
                            
                            <rect x="50" y="220" width="300" height="30" fill="#a8e6cf" stroke="#4caf50" stroke-width="2" rx="5"/>
                            <text x="200" y="240" text-anchor="middle" font-size="12" fill="#333">Base Conv + Scale</text>
                            
                            <line x1="200" y1="250" x2="200" y2="270" stroke="#333" stroke-width="2" marker-end="url(#arrowhead2)"/>
                            
                            <rect x="50" y="270" width="300" height="30" fill="#c8e6c9" stroke="#4caf50" stroke-width="2" rx="5"/>
                            <text x="200" y="290" text-anchor="middle" font-size="14" fill="#333">Output (N, C, H, W)</text>
                            
                            <defs>
                                <marker id="arrowhead2" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                                    <polygon points="0 0, 10 3.5, 0 7" fill="#333"/>
                                </marker>
                            </defs>
                        </svg>
                        <p><strong>优势：</strong></p>
                        <ul>
                            <li>时频域联合特征提取</li>
                            <li>多分辨率分析能力</li>
                            <li>自适应小波滤波器</li>
                        </ul>
                    </div>
                </div>

                <h4>🔧 核心代码对比</h4>
                <div class="method-steps">
                    <h5>原始ConvNeXt V2 Block实现</h5>
                    <div class="code-block">
class Block(nn.Module):
    def __init__(self, dim, drop_path=0.):
        super().__init__()
        # 标准深度卷积
        self.dwconv = nn.Conv2d(dim, dim, kernel_size=7, padding=3, groups=dim)
        self.norm = LayerNorm(dim, eps=1e-6)
        self.pwconv1 = nn.Linear(dim, 4 * dim)
        self.act = nn.GELU()
        self.grn = GRN(4 * dim)
        self.pwconv2 = nn.Linear(4 * dim, dim)
        self.drop_path = DropPath(drop_path) if drop_path > 0. else nn.Identity()

    def forward(self, x):
        input = x
        x = self.dwconv(x)  # 单一尺度深度卷积
        x = x.permute(0, 2, 3, 1)
        x = self.norm(x)
        x = self.pwconv1(x)
        x = self.act(x)
        x = self.grn(x)
        x = self.pwconv2(x)
        x = x.permute(0, 3, 1, 2)
        x = input + self.drop_path(x)
        return x
                    </div>
                </div>
            </div>
        </div>
