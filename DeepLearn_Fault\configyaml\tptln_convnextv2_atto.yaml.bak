# Theory-guided Progressive Transfer Learning Network (TPTLN) Configuration - Using ConvNeXtV2 Atto

# Model settings
model_name: tptln_convnextv2_atto

# Feature extractor pretrained settings (moved to top level)
pretrained: true
pretrained_path: /root/lanyun-tmp/DeepLearn_Fault/DeepLearn_Fault/checkpoint/convnextv2_atto/convnextv2_atto_1k_224_ema.pt

# Dataset settings
train_dataset: A_SLT  # Source domain
test_dataset: B_SLT   # Target domain
train: train
vali: val
test: test
data_dir: ./datasets/img_dataset_split

# Training settings
batch_size: 32
n_epoch: 50
early_stop: 20

# Optimizer settings
lr: 0.00005

momentum: 0.9
weight_decay: 1e-5

# Learning rate scheduler settings
lr_gamma: 0.0003
lr_decay: 0.75
lr_scheduler: true
num_workers: 20
# Confusion matrix usage
confmtx: 0

# t-SNE visualization
t_sne: 0

# Transfer learning settings (moved to top level)
transfer_loss_weight: 2.0
domain_adaptation_weight: 0.5
progressive_steps: 8
source_weight: 1.0
target_weight: 1.0 