Namespace(config='configyaml/ablation_wtconvnextv2_atto_nowavelet_ema.yaml', seed=1, num_workers=0, model_name='wtconvnextv2_atto', model_dir='DeepLearn_Fault/checkpoint/convnextv2_atto/convnextv2_atto_1k_224_ema.pt', use_wavelet=False, use_ema=True, data_dir='./datasets/img_dataset_split', train_dataset='A_SLT', test_dataset=None, train='train', vali='val', test='test', is_training=1, batch_size=16, n_epoch=30, early_stop=20, epoch_based_training=False, lr=0.0005, momentum=0.9, weight_decay=1e-05, lr_gamma=0.0003, lr_decay=0.75, lr_scheduler=True, t_sne=1, confmtx=1, device=device(type='cuda'))
{0: '0', 1: '1', 2: '2', 3: '3', 4: '4', 5: '5', 6: '6', 7: '7', 8: '8', 9: '9'}
{0: '0', 1: '1', 2: '2', 3: '3', 4: '4', 5: '5', 6: '6', 7: '7', 8: '8', 9: '9'}
{0: '0', 1: '1', 2: '2', 3: '3', 4: '4', 5: '5', 6: '6', 7: '7', 8: '8', 9: '9'}
ConvNeXtV2 配置: use_wavelet=False, use_ema=True
ModuleList(
  (0): Sequential(
    (0): Conv2d(3, 40, kernel_size=(4, 4), stride=(4, 4))
    (1): LayerNorm()
  )
  (1): Sequential(
    (0): LayerNorm()
    (1): Conv2d(40, 80, kernel_size=(2, 2), stride=(2, 2))
  )
  (2): Sequential(
    (0): LayerNorm()
    (1): Conv2d(80, 160, kernel_size=(2, 2), stride=(2, 2))
  )
  (3): Sequential(
    (0): LayerNorm()
    (1): Conv2d(160, 320, kernel_size=(2, 2), stride=(2, 2))
  )
)
ModuleList(
  (0): Sequential(
    (0): Block(
      (dwconv): Conv2d(40, 40, kernel_size=(7, 7), stride=(1, 1), padding=(3, 3), groups=40)
      (norm): LayerNorm()
      (pwconv1): Linear(in_features=40, out_features=160, bias=True)
      (act): GELU(approximate='none')
      (grn): GRN()
      (ema): EMA(
        (softmax): Softmax(dim=-1)
        (agp): AdaptiveAvgPool2d(output_size=(1, 1))
        (pool_h): AdaptiveAvgPool2d(output_size=(None, 1))
        (pool_w): AdaptiveAvgPool2d(output_size=(1, None))
        (gn): GroupNorm(5, 5, eps=1e-05, affine=True)
        (conv1x1): Conv2d(5, 5, kernel_size=(1, 1), stride=(1, 1))
        (conv3x3): Conv2d(5, 5, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
      )
      (pwconv2): Linear(in_features=160, out_features=40, bias=True)
      (drop_path): Identity()
    )
    (1): Block(
      (dwconv): Conv2d(40, 40, kernel_size=(7, 7), stride=(1, 1), padding=(3, 3), groups=40)
      (norm): LayerNorm()
      (pwconv1): Linear(in_features=40, out_features=160, bias=True)
      (act): GELU(approximate='none')
      (grn): GRN()
      (ema): EMA(
        (softmax): Softmax(dim=-1)
        (agp): AdaptiveAvgPool2d(output_size=(1, 1))
        (pool_h): AdaptiveAvgPool2d(output_size=(None, 1))
        (pool_w): AdaptiveAvgPool2d(output_size=(1, None))
        (gn): GroupNorm(5, 5, eps=1e-05, affine=True)
        (conv1x1): Conv2d(5, 5, kernel_size=(1, 1), stride=(1, 1))
        (conv3x3): Conv2d(5, 5, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
      )
      (pwconv2): Linear(in_features=160, out_features=40, bias=True)
      (drop_path): Identity()
    )
  )
  (1): Sequential(
    (0): Block(
      (dwconv): Conv2d(80, 80, kernel_size=(7, 7), stride=(1, 1), padding=(3, 3), groups=80)
      (norm): LayerNorm()
      (pwconv1): Linear(in_features=80, out_features=320, bias=True)
      (act): GELU(approximate='none')
      (grn): GRN()
      (ema): EMA(
        (softmax): Softmax(dim=-1)
        (agp): AdaptiveAvgPool2d(output_size=(1, 1))
        (pool_h): AdaptiveAvgPool2d(output_size=(None, 1))
        (pool_w): AdaptiveAvgPool2d(output_size=(1, None))
        (gn): GroupNorm(10, 10, eps=1e-05, affine=True)
        (conv1x1): Conv2d(10, 10, kernel_size=(1, 1), stride=(1, 1))
        (conv3x3): Conv2d(10, 10, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
      )
      (pwconv2): Linear(in_features=320, out_features=80, bias=True)
      (drop_path): Identity()
    )
    (1): Block(
      (dwconv): Conv2d(80, 80, kernel_size=(7, 7), stride=(1, 1), padding=(3, 3), groups=80)
      (norm): LayerNorm()
      (pwconv1): Linear(in_features=80, out_features=320, bias=True)
      (act): GELU(approximate='none')
      (grn): GRN()
      (ema): EMA(
        (softmax): Softmax(dim=-1)
        (agp): AdaptiveAvgPool2d(output_size=(1, 1))
        (pool_h): AdaptiveAvgPool2d(output_size=(None, 1))
        (pool_w): AdaptiveAvgPool2d(output_size=(1, None))
        (gn): GroupNorm(10, 10, eps=1e-05, affine=True)
        (conv1x1): Conv2d(10, 10, kernel_size=(1, 1), stride=(1, 1))
        (conv3x3): Conv2d(10, 10, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
      )
      (pwconv2): Linear(in_features=320, out_features=80, bias=True)
      (drop_path): Identity()
    )
  )
  (2): Sequential(
    (0): Block(
      (dwconv): Conv2d(160, 160, kernel_size=(7, 7), stride=(1, 1), padding=(3, 3), groups=160)
      (norm): LayerNorm()
      (pwconv1): Linear(in_features=160, out_features=640, bias=True)
      (act): GELU(approximate='none')
      (grn): GRN()
      (ema): EMA(
        (softmax): Softmax(dim=-1)
        (agp): AdaptiveAvgPool2d(output_size=(1, 1))
        (pool_h): AdaptiveAvgPool2d(output_size=(None, 1))
        (pool_w): AdaptiveAvgPool2d(output_size=(1, None))
        (gn): GroupNorm(20, 20, eps=1e-05, affine=True)
        (conv1x1): Conv2d(20, 20, kernel_size=(1, 1), stride=(1, 1))
        (conv3x3): Conv2d(20, 20, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
      )
      (pwconv2): Linear(in_features=640, out_features=160, bias=True)
      (drop_path): Identity()
    )
    (1): Block(
      (dwconv): Conv2d(160, 160, kernel_size=(7, 7), stride=(1, 1), padding=(3, 3), groups=160)
      (norm): LayerNorm()
      (pwconv1): Linear(in_features=160, out_features=640, bias=True)
      (act): GELU(approximate='none')
      (grn): GRN()
      (ema): EMA(
        (softmax): Softmax(dim=-1)
        (agp): AdaptiveAvgPool2d(output_size=(1, 1))
        (pool_h): AdaptiveAvgPool2d(output_size=(None, 1))
        (pool_w): AdaptiveAvgPool2d(output_size=(1, None))
        (gn): GroupNorm(20, 20, eps=1e-05, affine=True)
        (conv1x1): Conv2d(20, 20, kernel_size=(1, 1), stride=(1, 1))
        (conv3x3): Conv2d(20, 20, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
      )
      (pwconv2): Linear(in_features=640, out_features=160, bias=True)
      (drop_path): Identity()
    )
    (2): Block(
      (dwconv): Conv2d(160, 160, kernel_size=(7, 7), stride=(1, 1), padding=(3, 3), groups=160)
      (norm): LayerNorm()
      (pwconv1): Linear(in_features=160, out_features=640, bias=True)
      (act): GELU(approximate='none')
      (grn): GRN()
      (ema): EMA(
        (softmax): Softmax(dim=-1)
        (agp): AdaptiveAvgPool2d(output_size=(1, 1))
        (pool_h): AdaptiveAvgPool2d(output_size=(None, 1))
        (pool_w): AdaptiveAvgPool2d(output_size=(1, None))
        (gn): GroupNorm(20, 20, eps=1e-05, affine=True)
        (conv1x1): Conv2d(20, 20, kernel_size=(1, 1), stride=(1, 1))
        (conv3x3): Conv2d(20, 20, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
      )
      (pwconv2): Linear(in_features=640, out_features=160, bias=True)
      (drop_path): Identity()
    )
    (3): Block(
      (dwconv): Conv2d(160, 160, kernel_size=(7, 7), stride=(1, 1), padding=(3, 3), groups=160)
      (norm): LayerNorm()
      (pwconv1): Linear(in_features=160, out_features=640, bias=True)
      (act): GELU(approximate='none')
      (grn): GRN()
      (ema): EMA(
        (softmax): Softmax(dim=-1)
        (agp): AdaptiveAvgPool2d(output_size=(1, 1))
        (pool_h): AdaptiveAvgPool2d(output_size=(None, 1))
        (pool_w): AdaptiveAvgPool2d(output_size=(1, None))
        (gn): GroupNorm(20, 20, eps=1e-05, affine=True)
        (conv1x1): Conv2d(20, 20, kernel_size=(1, 1), stride=(1, 1))
        (conv3x3): Conv2d(20, 20, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
      )
      (pwconv2): Linear(in_features=640, out_features=160, bias=True)
      (drop_path): Identity()
    )
    (4): Block(
      (dwconv): Conv2d(160, 160, kernel_size=(7, 7), stride=(1, 1), padding=(3, 3), groups=160)
      (norm): LayerNorm()
      (pwconv1): Linear(in_features=160, out_features=640, bias=True)
      (act): GELU(approximate='none')
      (grn): GRN()
      (ema): EMA(
        (softmax): Softmax(dim=-1)
        (agp): AdaptiveAvgPool2d(output_size=(1, 1))
        (pool_h): AdaptiveAvgPool2d(output_size=(None, 1))
        (pool_w): AdaptiveAvgPool2d(output_size=(1, None))
        (gn): GroupNorm(20, 20, eps=1e-05, affine=True)
        (conv1x1): Conv2d(20, 20, kernel_size=(1, 1), stride=(1, 1))
        (conv3x3): Conv2d(20, 20, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
      )
      (pwconv2): Linear(in_features=640, out_features=160, bias=True)
      (drop_path): Identity()
    )
    (5): Block(
      (dwconv): Conv2d(160, 160, kernel_size=(7, 7), stride=(1, 1), padding=(3, 3), groups=160)
      (norm): LayerNorm()
      (pwconv1): Linear(in_features=160, out_features=640, bias=True)
      (act): GELU(approximate='none')
      (grn): GRN()
      (ema): EMA(
        (softmax): Softmax(dim=-1)
        (agp): AdaptiveAvgPool2d(output_size=(1, 1))
        (pool_h): AdaptiveAvgPool2d(output_size=(None, 1))
        (pool_w): AdaptiveAvgPool2d(output_size=(1, None))
        (gn): GroupNorm(20, 20, eps=1e-05, affine=True)
        (conv1x1): Conv2d(20, 20, kernel_size=(1, 1), stride=(1, 1))
        (conv3x3): Conv2d(20, 20, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
      )
      (pwconv2): Linear(in_features=640, out_features=160, bias=True)
      (drop_path): Identity()
    )
  )
  (3): Sequential(
    (0): Block(
      (dwconv): Conv2d(320, 320, kernel_size=(7, 7), stride=(1, 1), padding=(3, 3), groups=320)
      (norm): LayerNorm()
      (pwconv1): Linear(in_features=320, out_features=1280, bias=True)
      (act): GELU(approximate='none')
      (grn): GRN()
      (ema): EMA(
        (softmax): Softmax(dim=-1)
        (agp): AdaptiveAvgPool2d(output_size=(1, 1))
        (pool_h): AdaptiveAvgPool2d(output_size=(None, 1))
        (pool_w): AdaptiveAvgPool2d(output_size=(1, None))
        (gn): GroupNorm(40, 40, eps=1e-05, affine=True)
        (conv1x1): Conv2d(40, 40, kernel_size=(1, 1), stride=(1, 1))
        (conv3x3): Conv2d(40, 40, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
      )
      (pwconv2): Linear(in_features=1280, out_features=320, bias=True)
      (drop_path): Identity()
    )
    (1): Block(
      (dwconv): Conv2d(320, 320, kernel_size=(7, 7), stride=(1, 1), padding=(3, 3), groups=320)
      (norm): LayerNorm()
      (pwconv1): Linear(in_features=320, out_features=1280, bias=True)
      (act): GELU(approximate='none')
      (grn): GRN()
      (ema): EMA(
        (softmax): Softmax(dim=-1)
        (agp): AdaptiveAvgPool2d(output_size=(1, 1))
        (pool_h): AdaptiveAvgPool2d(output_size=(None, 1))
        (pool_w): AdaptiveAvgPool2d(output_size=(1, None))
        (gn): GroupNorm(40, 40, eps=1e-05, affine=True)
        (conv1x1): Conv2d(40, 40, kernel_size=(1, 1), stride=(1, 1))
        (conv3x3): Conv2d(40, 40, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
      )
      (pwconv2): Linear(in_features=1280, out_features=320, bias=True)
      (drop_path): Identity()
    )
  )
)
LayerNorm((320,), eps=1e-06, elementwise_affine=True)
Linear(in_features=320, out_features=10, bias=True)
模型结构:
ConvNeXtV2(
  (downsample_layers): ModuleList(
    (0): Sequential(
      (0): Conv2d(3, 40, kernel_size=(4, 4), stride=(4, 4))
      (1): LayerNorm()
    )
    (1): Sequential(
      (0): LayerNorm()
      (1): Conv2d(40, 80, kernel_size=(2, 2), stride=(2, 2))
    )
    (2): Sequential(
      (0): LayerNorm()
      (1): Conv2d(80, 160, kernel_size=(2, 2), stride=(2, 2))
    )
    (3): Sequential(
      (0): LayerNorm()
      (1): Conv2d(160, 320, kernel_size=(2, 2), stride=(2, 2))
    )
  )
  (stages): ModuleList(
    (0): Sequential(
      (0): Block(
        (dwconv): Conv2d(40, 40, kernel_size=(7, 7), stride=(1, 1), padding=(3, 3), groups=40)
        (norm): LayerNorm()
        (pwconv1): Linear(in_features=40, out_features=160, bias=True)
        (act): GELU(approximate='none')
        (grn): GRN()
        (ema): EMA(
          (softmax): Softmax(dim=-1)
          (agp): AdaptiveAvgPool2d(output_size=(1, 1))
          (pool_h): AdaptiveAvgPool2d(output_size=(None, 1))
          (pool_w): AdaptiveAvgPool2d(output_size=(1, None))
          (gn): GroupNorm(5, 5, eps=1e-05, affine=True)
          (conv1x1): Conv2d(5, 5, kernel_size=(1, 1), stride=(1, 1))
          (conv3x3): Conv2d(5, 5, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        )
        (pwconv2): Linear(in_features=160, out_features=40, bias=True)
        (drop_path): Identity()
      )
      (1): Block(
        (dwconv): Conv2d(40, 40, kernel_size=(7, 7), stride=(1, 1), padding=(3, 3), groups=40)
        (norm): LayerNorm()
        (pwconv1): Linear(in_features=40, out_features=160, bias=True)
        (act): GELU(approximate='none')
        (grn): GRN()
        (ema): EMA(
          (softmax): Softmax(dim=-1)
          (agp): AdaptiveAvgPool2d(output_size=(1, 1))
          (pool_h): AdaptiveAvgPool2d(output_size=(None, 1))
          (pool_w): AdaptiveAvgPool2d(output_size=(1, None))
          (gn): GroupNorm(5, 5, eps=1e-05, affine=True)
          (conv1x1): Conv2d(5, 5, kernel_size=(1, 1), stride=(1, 1))
          (conv3x3): Conv2d(5, 5, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        )
        (pwconv2): Linear(in_features=160, out_features=40, bias=True)
        (drop_path): Identity()
      )
    )
    (1): Sequential(
      (0): Block(
        (dwconv): Conv2d(80, 80, kernel_size=(7, 7), stride=(1, 1), padding=(3, 3), groups=80)
        (norm): LayerNorm()
        (pwconv1): Linear(in_features=80, out_features=320, bias=True)
        (act): GELU(approximate='none')
        (grn): GRN()
        (ema): EMA(
          (softmax): Softmax(dim=-1)
          (agp): AdaptiveAvgPool2d(output_size=(1, 1))
          (pool_h): AdaptiveAvgPool2d(output_size=(None, 1))
          (pool_w): AdaptiveAvgPool2d(output_size=(1, None))
          (gn): GroupNorm(10, 10, eps=1e-05, affine=True)
          (conv1x1): Conv2d(10, 10, kernel_size=(1, 1), stride=(1, 1))
          (conv3x3): Conv2d(10, 10, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        )
        (pwconv2): Linear(in_features=320, out_features=80, bias=True)
        (drop_path): Identity()
      )
      (1): Block(
        (dwconv): Conv2d(80, 80, kernel_size=(7, 7), stride=(1, 1), padding=(3, 3), groups=80)
        (norm): LayerNorm()
        (pwconv1): Linear(in_features=80, out_features=320, bias=True)
        (act): GELU(approximate='none')
        (grn): GRN()
        (ema): EMA(
          (softmax): Softmax(dim=-1)
          (agp): AdaptiveAvgPool2d(output_size=(1, 1))
          (pool_h): AdaptiveAvgPool2d(output_size=(None, 1))
          (pool_w): AdaptiveAvgPool2d(output_size=(1, None))
          (gn): GroupNorm(10, 10, eps=1e-05, affine=True)
          (conv1x1): Conv2d(10, 10, kernel_size=(1, 1), stride=(1, 1))
          (conv3x3): Conv2d(10, 10, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        )
        (pwconv2): Linear(in_features=320, out_features=80, bias=True)
        (drop_path): Identity()
      )
    )
    (2): Sequential(
      (0): Block(
        (dwconv): Conv2d(160, 160, kernel_size=(7, 7), stride=(1, 1), padding=(3, 3), groups=160)
        (norm): LayerNorm()
        (pwconv1): Linear(in_features=160, out_features=640, bias=True)
        (act): GELU(approximate='none')
        (grn): GRN()
        (ema): EMA(
          (softmax): Softmax(dim=-1)
          (agp): AdaptiveAvgPool2d(output_size=(1, 1))
          (pool_h): AdaptiveAvgPool2d(output_size=(None, 1))
          (pool_w): AdaptiveAvgPool2d(output_size=(1, None))
          (gn): GroupNorm(20, 20, eps=1e-05, affine=True)
          (conv1x1): Conv2d(20, 20, kernel_size=(1, 1), stride=(1, 1))
          (conv3x3): Conv2d(20, 20, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        )
        (pwconv2): Linear(in_features=640, out_features=160, bias=True)
        (drop_path): Identity()
      )
      (1): Block(
        (dwconv): Conv2d(160, 160, kernel_size=(7, 7), stride=(1, 1), padding=(3, 3), groups=160)
        (norm): LayerNorm()
        (pwconv1): Linear(in_features=160, out_features=640, bias=True)
        (act): GELU(approximate='none')
        (grn): GRN()
        (ema): EMA(
          (softmax): Softmax(dim=-1)
          (agp): AdaptiveAvgPool2d(output_size=(1, 1))
          (pool_h): AdaptiveAvgPool2d(output_size=(None, 1))
          (pool_w): AdaptiveAvgPool2d(output_size=(1, None))
          (gn): GroupNorm(20, 20, eps=1e-05, affine=True)
          (conv1x1): Conv2d(20, 20, kernel_size=(1, 1), stride=(1, 1))
          (conv3x3): Conv2d(20, 20, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        )
        (pwconv2): Linear(in_features=640, out_features=160, bias=True)
        (drop_path): Identity()
      )
      (2): Block(
        (dwconv): Conv2d(160, 160, kernel_size=(7, 7), stride=(1, 1), padding=(3, 3), groups=160)
        (norm): LayerNorm()
        (pwconv1): Linear(in_features=160, out_features=640, bias=True)
        (act): GELU(approximate='none')
        (grn): GRN()
        (ema): EMA(
          (softmax): Softmax(dim=-1)
          (agp): AdaptiveAvgPool2d(output_size=(1, 1))
          (pool_h): AdaptiveAvgPool2d(output_size=(None, 1))
          (pool_w): AdaptiveAvgPool2d(output_size=(1, None))
          (gn): GroupNorm(20, 20, eps=1e-05, affine=True)
          (conv1x1): Conv2d(20, 20, kernel_size=(1, 1), stride=(1, 1))
          (conv3x3): Conv2d(20, 20, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        )
        (pwconv2): Linear(in_features=640, out_features=160, bias=True)
        (drop_path): Identity()
      )
      (3): Block(
        (dwconv): Conv2d(160, 160, kernel_size=(7, 7), stride=(1, 1), padding=(3, 3), groups=160)
        (norm): LayerNorm()
        (pwconv1): Linear(in_features=160, out_features=640, bias=True)
        (act): GELU(approximate='none')
        (grn): GRN()
        (ema): EMA(
          (softmax): Softmax(dim=-1)
          (agp): AdaptiveAvgPool2d(output_size=(1, 1))
          (pool_h): AdaptiveAvgPool2d(output_size=(None, 1))
          (pool_w): AdaptiveAvgPool2d(output_size=(1, None))
          (gn): GroupNorm(20, 20, eps=1e-05, affine=True)
          (conv1x1): Conv2d(20, 20, kernel_size=(1, 1), stride=(1, 1))
          (conv3x3): Conv2d(20, 20, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        )
        (pwconv2): Linear(in_features=640, out_features=160, bias=True)
        (drop_path): Identity()
      )
      (4): Block(
        (dwconv): Conv2d(160, 160, kernel_size=(7, 7), stride=(1, 1), padding=(3, 3), groups=160)
        (norm): LayerNorm()
        (pwconv1): Linear(in_features=160, out_features=640, bias=True)
        (act): GELU(approximate='none')
        (grn): GRN()
        (ema): EMA(
          (softmax): Softmax(dim=-1)
          (agp): AdaptiveAvgPool2d(output_size=(1, 1))
          (pool_h): AdaptiveAvgPool2d(output_size=(None, 1))
          (pool_w): AdaptiveAvgPool2d(output_size=(1, None))
          (gn): GroupNorm(20, 20, eps=1e-05, affine=True)
          (conv1x1): Conv2d(20, 20, kernel_size=(1, 1), stride=(1, 1))
          (conv3x3): Conv2d(20, 20, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        )
        (pwconv2): Linear(in_features=640, out_features=160, bias=True)
        (drop_path): Identity()
      )
      (5): Block(
        (dwconv): Conv2d(160, 160, kernel_size=(7, 7), stride=(1, 1), padding=(3, 3), groups=160)
        (norm): LayerNorm()
        (pwconv1): Linear(in_features=160, out_features=640, bias=True)
        (act): GELU(approximate='none')
        (grn): GRN()
        (ema): EMA(
          (softmax): Softmax(dim=-1)
          (agp): AdaptiveAvgPool2d(output_size=(1, 1))
          (pool_h): AdaptiveAvgPool2d(output_size=(None, 1))
          (pool_w): AdaptiveAvgPool2d(output_size=(1, None))
          (gn): GroupNorm(20, 20, eps=1e-05, affine=True)
          (conv1x1): Conv2d(20, 20, kernel_size=(1, 1), stride=(1, 1))
          (conv3x3): Conv2d(20, 20, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        )
        (pwconv2): Linear(in_features=640, out_features=160, bias=True)
        (drop_path): Identity()
      )
    )
    (3): Sequential(
      (0): Block(
        (dwconv): Conv2d(320, 320, kernel_size=(7, 7), stride=(1, 1), padding=(3, 3), groups=320)
        (norm): LayerNorm()
        (pwconv1): Linear(in_features=320, out_features=1280, bias=True)
        (act): GELU(approximate='none')
        (grn): GRN()
        (ema): EMA(
          (softmax): Softmax(dim=-1)
          (agp): AdaptiveAvgPool2d(output_size=(1, 1))
          (pool_h): AdaptiveAvgPool2d(output_size=(None, 1))
          (pool_w): AdaptiveAvgPool2d(output_size=(1, None))
          (gn): GroupNorm(40, 40, eps=1e-05, affine=True)
          (conv1x1): Conv2d(40, 40, kernel_size=(1, 1), stride=(1, 1))
          (conv3x3): Conv2d(40, 40, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        )
        (pwconv2): Linear(in_features=1280, out_features=320, bias=True)
        (drop_path): Identity()
      )
      (1): Block(
        (dwconv): Conv2d(320, 320, kernel_size=(7, 7), stride=(1, 1), padding=(3, 3), groups=320)
        (norm): LayerNorm()
        (pwconv1): Linear(in_features=320, out_features=1280, bias=True)
        (act): GELU(approximate='none')
        (grn): GRN()
        (ema): EMA(
          (softmax): Softmax(dim=-1)
          (agp): AdaptiveAvgPool2d(output_size=(1, 1))
          (pool_h): AdaptiveAvgPool2d(output_size=(None, 1))
          (pool_w): AdaptiveAvgPool2d(output_size=(1, None))
          (gn): GroupNorm(40, 40, eps=1e-05, affine=True)
          (conv1x1): Conv2d(40, 40, kernel_size=(1, 1), stride=(1, 1))
          (conv3x3): Conv2d(40, 40, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        )
        (pwconv2): Linear(in_features=1280, out_features=320, bias=True)
        (drop_path): Identity()
      )
    )
  )
  (norm): LayerNorm((320,), eps=1e-06, elementwise_affine=True)
  (head): Linear(in_features=320, out_features=10, bias=True)
)
优化器: SGD (
Parameter Group 0
    dampening: 0
    differentiable: False
    foreach: None
    initial_lr: 0.0005
    lr: 0.0005
    maximize: False
    momentum: 0.9
    nesterov: False
    weight_decay: 1e-05
)
学习率: 0.0005
训练集大小: 1200
验证集大小: 400
测试集大小: 400
Epoch:[ 1/30],train_loss:2.3786 | vali_loss:2.322844,vali_acc:0.1000 | test_loss:2.322559,test_acc:0.1000 | lr: 0.000499

Epoch:[ 2/30],train_loss:2.3345 | vali_loss:2.317631,vali_acc:0.1975 | test_loss:2.316766,test_acc:0.1925 | lr: 0.000495

Epoch:[ 3/30],train_loss:2.2870 | vali_loss:2.148382,vali_acc:0.1925 | test_loss:2.139335,test_acc:0.1875 | lr: 0.000488

Epoch:[ 4/30],train_loss:2.1123 | vali_loss:2.002525,vali_acc:0.2325 | test_loss:2.005797,test_acc:0.2250 | lr: 0.000479

Epoch:[ 5/30],train_loss:1.8920 | vali_loss:1.696931,vali_acc:0.3650 | test_loss:1.714897,test_acc:0.3425 | lr: 0.000467

Epoch:[ 6/30],train_loss:1.7076 | vali_loss:1.505470,vali_acc:0.3800 | test_loss:1.517491,test_acc:0.3900 | lr: 0.000453

Epoch:[ 7/30],train_loss:1.5416 | vali_loss:1.389193,vali_acc:0.4675 | test_loss:1.422338,test_acc:0.4575 | lr: 0.000436

Epoch:[ 8/30],train_loss:1.3294 | vali_loss:1.211407,vali_acc:0.6075 | test_loss:1.249930,test_acc:0.6200 | lr: 0.000418

Epoch:[ 9/30],train_loss:1.1339 | vali_loss:1.470312,vali_acc:0.4500 | test_loss:1.515253,test_acc:0.4350 | lr: 0.000397

Epoch:[10/30],train_loss:0.9751 | vali_loss:0.888864,vali_acc:0.7675 | test_loss:0.962789,test_acc:0.7525 | lr: 0.000376

Epoch:[11/30],train_loss:0.7761 | vali_loss:0.988229,vali_acc:0.6050 | test_loss:1.074757,test_acc:0.5775 | lr: 0.000352

Epoch:[12/30],train_loss:0.6669 | vali_loss:0.696511,vali_acc:0.7825 | test_loss:0.759879,test_acc:0.7800 | lr: 0.000328

Epoch:[13/30],train_loss:0.5817 | vali_loss:0.570036,vali_acc:0.8450 | test_loss:0.613020,test_acc:0.8300 | lr: 0.000303

Epoch:[14/30],train_loss:0.5007 | vali_loss:0.759789,vali_acc:0.6775 | test_loss:0.820007,test_acc:0.6875 | lr: 0.000277

Epoch:[15/30],train_loss:0.4637 | vali_loss:0.549843,vali_acc:0.8375 | test_loss:0.616807,test_acc:0.8350 | lr: 0.000251

Epoch:[16/30],train_loss:0.4340 | vali_loss:0.558885,vali_acc:0.7650 | test_loss:0.594672,test_acc:0.7875 | lr: 0.000225

Epoch:[17/30],train_loss:0.3901 | vali_loss:0.470729,vali_acc:0.8825 | test_loss:0.497844,test_acc:0.8625 | lr: 0.000199

Epoch:[18/30],train_loss:0.3402 | vali_loss:0.455940,vali_acc:0.8250 | test_loss:0.494514,test_acc:0.8225 | lr: 0.000174

Epoch:[19/30],train_loss:0.4045 | vali_loss:0.400123,vali_acc:0.8800 | test_loss:0.454168,test_acc:0.8500 | lr: 0.000149

Epoch:[20/30],train_loss:0.3193 | vali_loss:0.312313,vali_acc:0.9200 | test_loss:0.359121,test_acc:0.8900 | lr: 0.000126

Epoch:[21/30],train_loss:0.2832 | vali_loss:0.413137,vali_acc:0.8575 | test_loss:0.474303,test_acc:0.8300 | lr: 0.000104

Epoch:[22/30],train_loss:0.2797 | vali_loss:0.346805,vali_acc:0.8700 | test_loss:0.385606,test_acc:0.8450 | lr: 0.000084

Epoch:[23/30],train_loss:0.2507 | vali_loss:0.338972,vali_acc:0.9050 | test_loss:0.373736,test_acc:0.8950 | lr: 0.000065

Epoch:[24/30],train_loss:0.2189 | vali_loss:0.297642,vali_acc:0.9175 | test_loss:0.334145,test_acc:0.9075 | lr: 0.000049

Epoch:[25/30],train_loss:0.2241 | vali_loss:0.335429,vali_acc:0.9050 | test_loss:0.371194,test_acc:0.8900 | lr: 0.000035

Epoch:[26/30],train_loss:0.2177 | vali_loss:0.287355,vali_acc:0.9125 | test_loss:0.331511,test_acc:0.8950 | lr: 0.000023

Epoch:[27/30],train_loss:0.1996 | vali_loss:0.288539,vali_acc:0.9150 | test_loss:0.321038,test_acc:0.9125 | lr: 0.000013

Epoch:[28/30],train_loss:0.2098 | vali_loss:0.316124,vali_acc:0.8950 | test_loss:0.352814,test_acc:0.8800 | lr: 0.000007

Epoch:[29/30],train_loss:0.1940 | vali_loss:0.303281,vali_acc:0.9000 | test_loss:0.343902,test_acc:0.8875 | lr: 0.000002

Epoch:[30/30],train_loss:0.1997 | vali_loss:0.309083,vali_acc:0.8900 | test_loss:0.347366,test_acc:0.8800 | lr: 0.000001

total cost time:837.1939406394958

Train best vali_acc:0.9200
test shape: torch.Size([400, 10]) torch.Size([400])
accuracy:0.8900
