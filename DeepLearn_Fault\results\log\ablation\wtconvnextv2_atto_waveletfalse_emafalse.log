Namespace(config='configyaml/ablation_wtconvnextv2_atto_nowavelet_noema.yaml', seed=1, num_workers=0, model_name='wtconvnextv2_atto', model_dir='DeepLearn_Fault/checkpoint/convnextv2_atto/convnextv2_atto_1k_224_ema.pt', use_wavelet=False, use_ema=False, data_dir='./datasets/img_dataset_split', train_dataset='A_SLT', test_dataset=None, train='train', vali='val', test='test', is_training=1, batch_size=16, n_epoch=30, early_stop=20, epoch_based_training=False, lr=0.0005, momentum=0.9, weight_decay=1e-05, lr_gamma=0.0003, lr_decay=0.75, lr_scheduler=True, t_sne=1, confmtx=1, device=device(type='cuda'))
{0: '0', 1: '1', 2: '2', 3: '3', 4: '4', 5: '5', 6: '6', 7: '7', 8: '8', 9: '9'}
{0: '0', 1: '1', 2: '2', 3: '3', 4: '4', 5: '5', 6: '6', 7: '7', 8: '8', 9: '9'}
{0: '0', 1: '1', 2: '2', 3: '3', 4: '4', 5: '5', 6: '6', 7: '7', 8: '8', 9: '9'}
ConvNeXtV2 配置: use_wavelet=False, use_ema=False
ModuleList(
  (0): Sequential(
    (0): Conv2d(3, 40, kernel_size=(4, 4), stride=(4, 4))
    (1): LayerNorm()
  )
  (1): Sequential(
    (0): LayerNorm()
    (1): Conv2d(40, 80, kernel_size=(2, 2), stride=(2, 2))
  )
  (2): Sequential(
    (0): LayerNorm()
    (1): Conv2d(80, 160, kernel_size=(2, 2), stride=(2, 2))
  )
  (3): Sequential(
    (0): LayerNorm()
    (1): Conv2d(160, 320, kernel_size=(2, 2), stride=(2, 2))
  )
)
ModuleList(
  (0): Sequential(
    (0): Block(
      (dwconv): Conv2d(40, 40, kernel_size=(7, 7), stride=(1, 1), padding=(3, 3), groups=40)
      (norm): LayerNorm()
      (pwconv1): Linear(in_features=40, out_features=160, bias=True)
      (act): GELU(approximate='none')
      (grn): GRN()
      (pwconv2): Linear(in_features=160, out_features=40, bias=True)
      (drop_path): Identity()
    )
    (1): Block(
      (dwconv): Conv2d(40, 40, kernel_size=(7, 7), stride=(1, 1), padding=(3, 3), groups=40)
      (norm): LayerNorm()
      (pwconv1): Linear(in_features=40, out_features=160, bias=True)
      (act): GELU(approximate='none')
      (grn): GRN()
      (pwconv2): Linear(in_features=160, out_features=40, bias=True)
      (drop_path): Identity()
    )
  )
  (1): Sequential(
    (0): Block(
      (dwconv): Conv2d(80, 80, kernel_size=(7, 7), stride=(1, 1), padding=(3, 3), groups=80)
      (norm): LayerNorm()
      (pwconv1): Linear(in_features=80, out_features=320, bias=True)
      (act): GELU(approximate='none')
      (grn): GRN()
      (pwconv2): Linear(in_features=320, out_features=80, bias=True)
      (drop_path): Identity()
    )
    (1): Block(
      (dwconv): Conv2d(80, 80, kernel_size=(7, 7), stride=(1, 1), padding=(3, 3), groups=80)
      (norm): LayerNorm()
      (pwconv1): Linear(in_features=80, out_features=320, bias=True)
      (act): GELU(approximate='none')
      (grn): GRN()
      (pwconv2): Linear(in_features=320, out_features=80, bias=True)
      (drop_path): Identity()
    )
  )
  (2): Sequential(
    (0): Block(
      (dwconv): Conv2d(160, 160, kernel_size=(7, 7), stride=(1, 1), padding=(3, 3), groups=160)
      (norm): LayerNorm()
      (pwconv1): Linear(in_features=160, out_features=640, bias=True)
      (act): GELU(approximate='none')
      (grn): GRN()
      (pwconv2): Linear(in_features=640, out_features=160, bias=True)
      (drop_path): Identity()
    )
    (1): Block(
      (dwconv): Conv2d(160, 160, kernel_size=(7, 7), stride=(1, 1), padding=(3, 3), groups=160)
      (norm): LayerNorm()
      (pwconv1): Linear(in_features=160, out_features=640, bias=True)
      (act): GELU(approximate='none')
      (grn): GRN()
      (pwconv2): Linear(in_features=640, out_features=160, bias=True)
      (drop_path): Identity()
    )
    (2): Block(
      (dwconv): Conv2d(160, 160, kernel_size=(7, 7), stride=(1, 1), padding=(3, 3), groups=160)
      (norm): LayerNorm()
      (pwconv1): Linear(in_features=160, out_features=640, bias=True)
      (act): GELU(approximate='none')
      (grn): GRN()
      (pwconv2): Linear(in_features=640, out_features=160, bias=True)
      (drop_path): Identity()
    )
    (3): Block(
      (dwconv): Conv2d(160, 160, kernel_size=(7, 7), stride=(1, 1), padding=(3, 3), groups=160)
      (norm): LayerNorm()
      (pwconv1): Linear(in_features=160, out_features=640, bias=True)
      (act): GELU(approximate='none')
      (grn): GRN()
      (pwconv2): Linear(in_features=640, out_features=160, bias=True)
      (drop_path): Identity()
    )
    (4): Block(
      (dwconv): Conv2d(160, 160, kernel_size=(7, 7), stride=(1, 1), padding=(3, 3), groups=160)
      (norm): LayerNorm()
      (pwconv1): Linear(in_features=160, out_features=640, bias=True)
      (act): GELU(approximate='none')
      (grn): GRN()
      (pwconv2): Linear(in_features=640, out_features=160, bias=True)
      (drop_path): Identity()
    )
    (5): Block(
      (dwconv): Conv2d(160, 160, kernel_size=(7, 7), stride=(1, 1), padding=(3, 3), groups=160)
      (norm): LayerNorm()
      (pwconv1): Linear(in_features=160, out_features=640, bias=True)
      (act): GELU(approximate='none')
      (grn): GRN()
      (pwconv2): Linear(in_features=640, out_features=160, bias=True)
      (drop_path): Identity()
    )
  )
  (3): Sequential(
    (0): Block(
      (dwconv): Conv2d(320, 320, kernel_size=(7, 7), stride=(1, 1), padding=(3, 3), groups=320)
      (norm): LayerNorm()
      (pwconv1): Linear(in_features=320, out_features=1280, bias=True)
      (act): GELU(approximate='none')
      (grn): GRN()
      (pwconv2): Linear(in_features=1280, out_features=320, bias=True)
      (drop_path): Identity()
    )
    (1): Block(
      (dwconv): Conv2d(320, 320, kernel_size=(7, 7), stride=(1, 1), padding=(3, 3), groups=320)
      (norm): LayerNorm()
      (pwconv1): Linear(in_features=320, out_features=1280, bias=True)
      (act): GELU(approximate='none')
      (grn): GRN()
      (pwconv2): Linear(in_features=1280, out_features=320, bias=True)
      (drop_path): Identity()
    )
  )
)
LayerNorm((320,), eps=1e-06, elementwise_affine=True)
Linear(in_features=320, out_features=10, bias=True)
模型结构:
ConvNeXtV2(
  (downsample_layers): ModuleList(
    (0): Sequential(
      (0): Conv2d(3, 40, kernel_size=(4, 4), stride=(4, 4))
      (1): LayerNorm()
    )
    (1): Sequential(
      (0): LayerNorm()
      (1): Conv2d(40, 80, kernel_size=(2, 2), stride=(2, 2))
    )
    (2): Sequential(
      (0): LayerNorm()
      (1): Conv2d(80, 160, kernel_size=(2, 2), stride=(2, 2))
    )
    (3): Sequential(
      (0): LayerNorm()
      (1): Conv2d(160, 320, kernel_size=(2, 2), stride=(2, 2))
    )
  )
  (stages): ModuleList(
    (0): Sequential(
      (0): Block(
        (dwconv): Conv2d(40, 40, kernel_size=(7, 7), stride=(1, 1), padding=(3, 3), groups=40)
        (norm): LayerNorm()
        (pwconv1): Linear(in_features=40, out_features=160, bias=True)
        (act): GELU(approximate='none')
        (grn): GRN()
        (pwconv2): Linear(in_features=160, out_features=40, bias=True)
        (drop_path): Identity()
      )
      (1): Block(
        (dwconv): Conv2d(40, 40, kernel_size=(7, 7), stride=(1, 1), padding=(3, 3), groups=40)
        (norm): LayerNorm()
        (pwconv1): Linear(in_features=40, out_features=160, bias=True)
        (act): GELU(approximate='none')
        (grn): GRN()
        (pwconv2): Linear(in_features=160, out_features=40, bias=True)
        (drop_path): Identity()
      )
    )
    (1): Sequential(
      (0): Block(
        (dwconv): Conv2d(80, 80, kernel_size=(7, 7), stride=(1, 1), padding=(3, 3), groups=80)
        (norm): LayerNorm()
        (pwconv1): Linear(in_features=80, out_features=320, bias=True)
        (act): GELU(approximate='none')
        (grn): GRN()
        (pwconv2): Linear(in_features=320, out_features=80, bias=True)
        (drop_path): Identity()
      )
      (1): Block(
        (dwconv): Conv2d(80, 80, kernel_size=(7, 7), stride=(1, 1), padding=(3, 3), groups=80)
        (norm): LayerNorm()
        (pwconv1): Linear(in_features=80, out_features=320, bias=True)
        (act): GELU(approximate='none')
        (grn): GRN()
        (pwconv2): Linear(in_features=320, out_features=80, bias=True)
        (drop_path): Identity()
      )
    )
    (2): Sequential(
      (0): Block(
        (dwconv): Conv2d(160, 160, kernel_size=(7, 7), stride=(1, 1), padding=(3, 3), groups=160)
        (norm): LayerNorm()
        (pwconv1): Linear(in_features=160, out_features=640, bias=True)
        (act): GELU(approximate='none')
        (grn): GRN()
        (pwconv2): Linear(in_features=640, out_features=160, bias=True)
        (drop_path): Identity()
      )
      (1): Block(
        (dwconv): Conv2d(160, 160, kernel_size=(7, 7), stride=(1, 1), padding=(3, 3), groups=160)
        (norm): LayerNorm()
        (pwconv1): Linear(in_features=160, out_features=640, bias=True)
        (act): GELU(approximate='none')
        (grn): GRN()
        (pwconv2): Linear(in_features=640, out_features=160, bias=True)
        (drop_path): Identity()
      )
      (2): Block(
        (dwconv): Conv2d(160, 160, kernel_size=(7, 7), stride=(1, 1), padding=(3, 3), groups=160)
        (norm): LayerNorm()
        (pwconv1): Linear(in_features=160, out_features=640, bias=True)
        (act): GELU(approximate='none')
        (grn): GRN()
        (pwconv2): Linear(in_features=640, out_features=160, bias=True)
        (drop_path): Identity()
      )
      (3): Block(
        (dwconv): Conv2d(160, 160, kernel_size=(7, 7), stride=(1, 1), padding=(3, 3), groups=160)
        (norm): LayerNorm()
        (pwconv1): Linear(in_features=160, out_features=640, bias=True)
        (act): GELU(approximate='none')
        (grn): GRN()
        (pwconv2): Linear(in_features=640, out_features=160, bias=True)
        (drop_path): Identity()
      )
      (4): Block(
        (dwconv): Conv2d(160, 160, kernel_size=(7, 7), stride=(1, 1), padding=(3, 3), groups=160)
        (norm): LayerNorm()
        (pwconv1): Linear(in_features=160, out_features=640, bias=True)
        (act): GELU(approximate='none')
        (grn): GRN()
        (pwconv2): Linear(in_features=640, out_features=160, bias=True)
        (drop_path): Identity()
      )
      (5): Block(
        (dwconv): Conv2d(160, 160, kernel_size=(7, 7), stride=(1, 1), padding=(3, 3), groups=160)
        (norm): LayerNorm()
        (pwconv1): Linear(in_features=160, out_features=640, bias=True)
        (act): GELU(approximate='none')
        (grn): GRN()
        (pwconv2): Linear(in_features=640, out_features=160, bias=True)
        (drop_path): Identity()
      )
    )
    (3): Sequential(
      (0): Block(
        (dwconv): Conv2d(320, 320, kernel_size=(7, 7), stride=(1, 1), padding=(3, 3), groups=320)
        (norm): LayerNorm()
        (pwconv1): Linear(in_features=320, out_features=1280, bias=True)
        (act): GELU(approximate='none')
        (grn): GRN()
        (pwconv2): Linear(in_features=1280, out_features=320, bias=True)
        (drop_path): Identity()
      )
      (1): Block(
        (dwconv): Conv2d(320, 320, kernel_size=(7, 7), stride=(1, 1), padding=(3, 3), groups=320)
        (norm): LayerNorm()
        (pwconv1): Linear(in_features=320, out_features=1280, bias=True)
        (act): GELU(approximate='none')
        (grn): GRN()
        (pwconv2): Linear(in_features=1280, out_features=320, bias=True)
        (drop_path): Identity()
      )
    )
  )
  (norm): LayerNorm((320,), eps=1e-06, elementwise_affine=True)
  (head): Linear(in_features=320, out_features=10, bias=True)
)
优化器: SGD (
Parameter Group 0
    dampening: 0
    differentiable: False
    foreach: None
    initial_lr: 0.0005
    lr: 0.0005
    maximize: False
    momentum: 0.9
    nesterov: False
    weight_decay: 1e-05
)
学习率: 0.0005
训练集大小: 1200
验证集大小: 400
测试集大小: 400
Epoch:[ 1/30],train_loss:2.3479 | vali_loss:2.311803,vali_acc:0.1000 | test_loss:2.311559,test_acc:0.1050 | lr: 0.000499

Epoch:[ 2/30],train_loss:2.3158 | vali_loss:2.333315,vali_acc:0.1000 | test_loss:2.332148,test_acc:0.1000 | lr: 0.000495

Epoch:[ 3/30],train_loss:2.1906 | vali_loss:1.863163,vali_acc:0.3475 | test_loss:1.866026,test_acc:0.3575 | lr: 0.000488

Epoch:[ 4/30],train_loss:1.7862 | vali_loss:2.085003,vali_acc:0.1225 | test_loss:2.104641,test_acc:0.1250 | lr: 0.000479

Epoch:[ 5/30],train_loss:1.4987 | vali_loss:1.379267,vali_acc:0.4750 | test_loss:1.416123,test_acc:0.4500 | lr: 0.000467

Epoch:[ 6/30],train_loss:1.1004 | vali_loss:0.923339,vali_acc:0.7750 | test_loss:0.964885,test_acc:0.8025 | lr: 0.000453

Epoch:[ 7/30],train_loss:0.9182 | vali_loss:1.146284,vali_acc:0.5175 | test_loss:1.207005,test_acc:0.4850 | lr: 0.000436

Epoch:[ 8/30],train_loss:0.7041 | vali_loss:1.079568,vali_acc:0.5600 | test_loss:1.132632,test_acc:0.5375 | lr: 0.000418

Epoch:[ 9/30],train_loss:0.6010 | vali_loss:0.789749,vali_acc:0.6700 | test_loss:0.846024,test_acc:0.6650 | lr: 0.000397

Epoch:[10/30],train_loss:0.6010 | vali_loss:1.097652,vali_acc:0.5125 | test_loss:1.158969,test_acc:0.4925 | lr: 0.000376

Epoch:[11/30],train_loss:0.4662 | vali_loss:0.617303,vali_acc:0.7875 | test_loss:0.659938,test_acc:0.7500 | lr: 0.000352

Epoch:[12/30],train_loss:0.2997 | vali_loss:0.511180,vali_acc:0.7850 | test_loss:0.532800,test_acc:0.8000 | lr: 0.000328

Epoch:[13/30],train_loss:0.3152 | vali_loss:0.373682,vali_acc:0.8700 | test_loss:0.432445,test_acc:0.8275 | lr: 0.000303

Epoch:[14/30],train_loss:0.2342 | vali_loss:0.361562,vali_acc:0.8900 | test_loss:0.403440,test_acc:0.8825 | lr: 0.000277

Epoch:[15/30],train_loss:0.2346 | vali_loss:0.441919,vali_acc:0.8300 | test_loss:0.483711,test_acc:0.8075 | lr: 0.000251

Epoch:[16/30],train_loss:0.1940 | vali_loss:0.897088,vali_acc:0.6175 | test_loss:0.875739,test_acc:0.6475 | lr: 0.000225

Epoch:[17/30],train_loss:0.2170 | vali_loss:0.565668,vali_acc:0.7450 | test_loss:0.558435,test_acc:0.7750 | lr: 0.000199

Epoch:[18/30],train_loss:0.1746 | vali_loss:0.340825,vali_acc:0.8600 | test_loss:0.385371,test_acc:0.8475 | lr: 0.000174

Epoch:[19/30],train_loss:0.1388 | vali_loss:0.285043,vali_acc:0.9150 | test_loss:0.333921,test_acc:0.8925 | lr: 0.000149

Epoch:[20/30],train_loss:0.1549 | vali_loss:0.697818,vali_acc:0.7375 | test_loss:0.674578,test_acc:0.7575 | lr: 0.000126

Epoch:[21/30],train_loss:0.1342 | vali_loss:0.337046,vali_acc:0.8675 | test_loss:0.351273,test_acc:0.8775 | lr: 0.000104

Epoch:[22/30],train_loss:0.1158 | vali_loss:0.286832,vali_acc:0.9100 | test_loss:0.317649,test_acc:0.8775 | lr: 0.000084

Epoch:[23/30],train_loss:0.1101 | vali_loss:0.245260,vali_acc:0.9275 | test_loss:0.272573,test_acc:0.9125 | lr: 0.000065

Epoch:[24/30],train_loss:0.0977 | vali_loss:0.367571,vali_acc:0.8525 | test_loss:0.371647,test_acc:0.8575 | lr: 0.000049

Epoch:[25/30],train_loss:0.0842 | vali_loss:0.246026,vali_acc:0.9325 | test_loss:0.278342,test_acc:0.9050 | lr: 0.000035

Epoch:[26/30],train_loss:0.0859 | vali_loss:0.294673,vali_acc:0.8875 | test_loss:0.314998,test_acc:0.8875 | lr: 0.000023

Epoch:[27/30],train_loss:0.0841 | vali_loss:0.269831,vali_acc:0.9075 | test_loss:0.295514,test_acc:0.8950 | lr: 0.000013

Epoch:[28/30],train_loss:0.0836 | vali_loss:0.260470,vali_acc:0.9200 | test_loss:0.287620,test_acc:0.9050 | lr: 0.000007

Epoch:[29/30],train_loss:0.0825 | vali_loss:0.267099,vali_acc:0.9175 | test_loss:0.292496,test_acc:0.9025 | lr: 0.000002

Epoch:[30/30],train_loss:0.0798 | vali_loss:0.274163,vali_acc:0.9100 | test_loss:0.298112,test_acc:0.8975 | lr: 0.000001

total cost time:825.5637717247009

Train best vali_acc:0.9325
test shape: torch.Size([400, 10]) torch.Size([400])
accuracy:0.9050
