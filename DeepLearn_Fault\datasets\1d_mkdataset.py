#!/usr/bin/env python
# @Time    : 2020/7/8 14:34
# <AUTHOR> wb
# @File    : data_preprocess.py

'''
数据预处理文件
处理原始的mat文件,将mat文件转换为h5py保存的数据

将CWRU原始数据分为驱动端与风扇端(DE,FE),根据转速与故障将数据分为101种类别
其中训练样本80%,测试样本20%，随机打乱取样。
最后数据以h5格式存储,其中DE为驱动端测点的数据集,包含训练样本与测试样本;FE为风扇端。

读入数据之后将按400个采样点进行切分
'''

import os
import numpy as np
import pandas as pd
import scipy.io as scio
import h5py
from sklearn.model_selection import train_test_split

class CWRULoader():

    def __init__(self, root, list_filename):
        '''
        读取数据集的基本信息
        :param root: 数据集根目录
        :param list_filename: 因为是多个文件，所以需要一张文件列表
        '''
        self.root = root
        if list_filename != None:
            self.frame = pd.read_table(list_filename,sep=',')
            self.len = len(self.frame['file_name'])
        else:
            raise ValueError("file_list [%s] not recognized." % list_filename)

    def load_mat_make_dataset(self, test_size, sample_num, len=1024, shift=512):
        '''
        处理数据集，划分训练集，测试集
        :param train_fraction: 划分训练集的比例
        :return:
        '''
        # 设置空数组
        self.num_classes = []
        for idx in range(self.len):
            ##加载数据
            mat_name = os.path.join(self.root, self.frame['file_name'][idx])
            raw_data = scio.loadmat(mat_name)
            name = self.frame['file_name'][idx].split('.')[:-1]
            name = name[0]
            self.num_classes.append(name)
            if name == '97':
                name = '097'
            db_data = raw_data['X' + name + '_DE_time'].reshape(-1)
            
            db_data = (db_data - min(db_data))/ (max(db_data) - min(db_data)) # 归一化
            # 划分数据集合
            i = 0
            sum = shift * sample_num
            tmp_data = []
            tmp_label = []
            while i< sum:
                # 划分数据集
                data = db_data[i:i+len]
                tmp_data.append(data)
                tmp_label.append(self.frame['label'][idx])
                i += shift # 移动
            tmp_data = np.array(tmp_data)
            tmp_label= np.array(tmp_label)
            # 划分训练集和验证集
            X_tra, X_te, y_tra, y_te = train_test_split(tmp_data, tmp_label, test_size=test_size, random_state=42)
            """
            X_train.append(X_tra)
            X_vali.append(X_te)
            y_train.append(y_tra)
            y_vali.append(y_te)
            """
            if idx == 0:
                self.X_train = X_tra
                self.y_train = y_tra
                self.X_test  = X_te
                self.y_test  = y_te
            else:
                self.X_train = np.concatenate((self.X_train, X_tra), axis=0)
                self.y_train = np.concatenate((self.y_train, y_tra), axis=0)
                self.X_test = np.concatenate((self.X_test, X_te), axis=0)
                self.y_test = np.concatenate((self.y_test, y_te), axis=0)
        return self.X_train, self.X_test, self.y_train, self.y_test, self.num_classes
            
    def save(self, filename):
        '''
        将上面处理好的文件保存为h5文件
        :param filename:保存的文件名
        :param X_train:输入训练集
        :param y_train:训练集标签
        :param X_test:输入测试集
        :param y_test:测试集标签
        :return:
        '''
        f = h5py.File(filename, 'w')
        f.create_dataset('num_classes', data=self.num_classes)
        f.create_dataset('X_train', data=self.X_train)
        f.create_dataset('y_train', data=self.y_train)
        f.create_dataset('X_test', data=self.X_test)
        f.create_dataset('y_test', data=self.y_test)
        f.close()

if __name__ == '__main__':
    Dp = CWRULoader('datasetsbuid/datasets_1d/CWRU_DE', './datasetsbuid/makedataset.txt')
    X_train, X_vali,y_train, y_vali, num_classes = Dp.load_mat_make_dataset(0.2, 100, 1024, 1024) #测试集比例，划分总个数，长度，滑动量
    Dp.save('CWRU_C10_L1024_S0_N100.h5')
    print("X_train-{},X_test-{},y_train-{},y_test-{}".format(X_train.shape, X_vali.shape, y_train.shape, y_vali.shape))
    #print('h5文件保存成功')