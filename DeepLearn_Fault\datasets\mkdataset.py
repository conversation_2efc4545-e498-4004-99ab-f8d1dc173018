import os
import pywt
import matplotlib.pyplot as plt
import numpy as np
import scipy.io as sio
import math
from pyts.image import GramianAngularField, MarkovTransitionField
import argparse
from scipy import signal, fftpack
from superlet import superlets, SuperletTransform
def get_parser():
    parser = argparse.ArgumentParser(description='make datasets')

    parser.add_argument('--data_dir', type=str, help='data file')
    parser.add_argument('--save_dir', type=str, help='data save file')
    parser.add_argument('--data', type=str, required=True, default='CWRU_DE', help='dataset type')
    parser.add_argument('--work_state', type=str, help='work_state is use PBU ')

    parser.add_argument('--plg_type', type=str, required=True, default='CWT', help='Signal processing method')
    parser.add_argument('--dataname', nargs='+', help='dataset name')
    parser.add_argument('--length', type=int, default=1024, help='sample length')
    parser.add_argument('--plg_num', type=int, default=200, help='make the number of samples')
    parser.add_argument('--fs', type=int, default=12000, help='sampling rate')
    parser.add_argument('--movement', type=int, default=512, help='sample movement')
    parser.add_argument('--ylim', type=int, default=6000, help='y axis limit range')
    args = parser.parse_args()
    return args

def load_dataset(filepath, whichdb, dataname, args):
    '''
    读取.mat文件中的振动数据

    filepath: 文件路径
    dataname: 数据集名
    whichdb : 数据集类型 
    '''
    vibration_data = []
    if whichdb == 'CWRU_DE':
        filepath = os.path.join(filepath, whichdb, dataname + '.mat')
        data = sio.loadmat(filepath)
        if len(dataname) == 2:
            dataname = '0' + dataname
        vibration_data = data['X' + dataname + '_DE_time'].reshape(-1)
    elif whichdb == 'CWRU_FE': # 美国西储大学数据集
        filepath = os.path.join(filepath, whichdb, dataname + '.mat')
        data = sio.loadmat(filepath)
        vibration_data = data['X' + dataname + '_FE_time'].reshape(-1)
    elif whichdb == 'JNU': # 江南大学数据集
        filepath = os.path.join(filepath, whichdb, dataname + '.mat')
        data = sio.loadmat(filepath)
        vibration_data = data[dataname].reshape(-1)
    elif whichdb == 'HIT': # 哈尔滨工业大学数据集
        filepath = os.path.join(filepath, whichdb, dataname + '.npy')
        vibration_data = np.load(filepath) 
    elif whichdb == 'PBU':
        # 当前文件可以构建的样本数
        num = (args.fs * 4- args.length) // args.movement + 1
        # 当个文件构建样本所需长度
        N = (num - 1) * args.movement + args.length
        # 需要的文件数
        file_num = math.ceil(args.plg_num / num)
        for i in range(file_num):
            dataset = '{}_{}_{}'.format(args.work_state, dataname, i+1)
            new_filepath = os.path.join(filepath, whichdb, dataname, dataset + '.mat')
            
            tmp_data = sio.loadmat(new_filepath)
            tmp_data = tmp_data[dataset]['Y']
            tmp_data = tmp_data[0][0][0][6][2].reshape(-1)
            #vibration_data += tmp_data[:N]
            vibration_data = np.concatenate((vibration_data, tmp_data[:N]))

    return vibration_data

def plt_one_cwt(filepath, length, whichdb, sr):
    wavename = 'cmor3-3' # 'cmor3-3'
    totalscal = 128       # 尺度序列长度
    fc = pywt.central_frequency(wavename)  # 计算小波函数的中心频率
    cparam = 2 * fc * totalscal            # 常数c
    scales = cparam / np.arange(totalscal, 1, -1)  # 为使转换后的频率序列是一等差序列，尺度序列必须取为这一形式（也即小波尺度）
    filenames = os.listdir(filepath+ '/'+ whichdb)
    num = len(filenames)
    rows = math.floor( math.sqrt( len(filenames) ) )
    cols = math.ceil( num/rows )
    i = 0
    for name in filenames:
        name = name.split(".")[0]    # 去后缀
        db_data = load_dataset(filepath, name, length, whichdb, args)
        #data = (db_data - min(db_data))/ (max(db_data) - min(db_data))
        t = np.arange(0, length/sr, 1.0/sr)
        
        [cwtmatr, frequencies] = pywt.cwt(db_data, scales, wavename, 1.0 / sr)#4.y为将要进行cwt变换的一维输入信号
    
        t = np.arange(0, length/sr, 1.0/sr)
        plt.contourf(t, frequencies, abs(cwtmatr))
        plt.title(name)
        plt.ylabel(u"freq(Hz)")
        plt.xlabel(u"time(s)")
        plt.subplots_adjust(hspace=0.5,wspace=0.5)
        i = i+1
    plt.show()

class plt_2d_dataset():
    def __init__(self, args) -> None:
        '''
        filepath: 文件路径
        whichdb : 数据集类型 
        dataname: 数据集名
        length  : 绘制长度
        plg_num : 绘制图片张数
        fs      : 采样率
        '''
        self.filepath = args.data_dir
        self.whichdb = args.data
        self.datanames = args.dataname
        self.length = args.length
        self.plg_num = args.plg_num
        self.plg_type = args.plg_type
        self.fs = args.fs
        self.movement = args.movement
        self.savepath = args.save_dir
        self.ylim = args.ylim

    def plt_stft(self):
        # 设置绘制图形参数
        plt.axis('off')                           #关闭坐标轴
        plt.figure(figsize=(2.2, 2.2), dpi=100)   # 设置图片分辨率220*220
        plt.subplots_adjust(top=1, bottom=0, right=1, left=0,hspace=0, wspace=0)  # 边界与画布重合
        plt.margins(0, 0)  # 设置图形边距
        for idx, name in enumerate(self.datanames):
            # 创建文件保存路径
            savepath = self.savepath + '/' + str(idx)
            if not os.path.exists(savepath):
                os.makedirs(savepath)
            # 加载数据
            db_data = load_dataset(self.filepath, self.whichdb, name, args)
            #db_data = (db_data - min(db_data))/ (max(db_data) - min(db_data)) # 归一化
            # 开始绘制
            i = 0
            index = 1
            #plt.gca().invert_yaxis()                  # 翻转y轴
            num = (args.fs * 4- args.length) / args.movement + 1
            if num > args.plg_num:
                sum = self.movement * self.plg_num
            else:
                sum = self.movement * (self.plg_num + 3)
            while i< sum:
                if self.whichdb == 'PBU' and index % (num+1) ==0:
                    i += self.movement
                data= db_data[i:i+self.length]
                i += self.movement  # 移动
                f,st,zxx = signal.stft(data, self.fs, nperseg=50, noverlap=None)
                plt.contourf(st, f, abs(zxx),cmap='jet')
                plt.ylim(0, self.ylim)
                plt.savefig('{}/{}.jpeg'.format(savepath, index))
                plt.clf()
                index += 1

    def plt_cwt(self):
        wavename = 'cmor3-3'  # 'cmor3-3'
        totalscal = 256       # 尺度序列长度
        fc = pywt.central_frequency(wavename)    # 计算小波函数的中心频率
        cparam = 2 * fc * totalscal              # 常数c
        scales = cparam / np.arange(totalscal, 1, -1)  # 为使转换后的频率序列是一等差序列，尺度序列必须取为这一形式（也即小波尺度）
        # 设置绘制图形参数
        plt.axis('off')                           #关闭坐标轴
        plt.figure(figsize=(2.2, 2.2), dpi=100)   # 设置图片分辨率220*220
        plt.subplots_adjust(top=1, bottom=0, right=1, left=0,hspace=0, wspace=0)  # 边界与画布重合
        plt.margins(0, 0)  # 设置图形边距
        for idx, name in enumerate(self.datanames):
            # 创建文件保存路径
            savepath = self.savepath + '/' + str(idx)
            if not os.path.exists(savepath):
                os.makedirs(savepath)
            # 加载数据
            db_data = load_dataset(self.filepath, self.whichdb, name, args)
            #db_data = (db_data - min(db_data))/ (max(db_data) - min(db_data)) # 归一化
            # 开始绘制
            i = 0
            index = 1
            
            t = np.arange(0, self.length/self.fs, 1.0/self.fs)
            #plt.gca().invert_yaxis()                  # 翻转y轴
            num = (args.fs * 4- args.length) / args.movement + 1
            if num > args.plg_num:
                sum = self.movement * self.plg_num
            else:
                sum = self.movement * (self.plg_num + 3)
            while i< sum:
                if self.whichdb == 'PBU' and index % (num+1) ==0:
                    i += self.movement
                data= db_data[i:i+self.length]
                i += self.movement  # 移动
                [cwtmatr, freq] = pywt.cwt(data, scales, wavename, 1.0/self.fs)#4.y为将要进行cwt变换的一维输入信号
                plt.contourf(t, freq, abs(cwtmatr), cmap='jet')
                plt.ylim(0, self.ylim)
                plt.savefig('{}/{}.jpeg'.format(savepath, index))
                plt.clf()
                index += 1
    
    def plt_superlet(self):
        #设置绘制图形参数
        plt.axis('off')                           #关闭坐标轴
        plt.figure(figsize=(2.2, 2.2), dpi=100)   # 设置图片分辨率220*220
        plt.subplots_adjust(top=1, bottom=0, right=1, left=0,hspace=0, wspace=0)  # 边界与画布重合
        plt.margins(0, 0)  # 设置图形边距

        for idx, name in enumerate(self.datanames):
           #创建文件保存路径
            savepath = self.savepath +'/'+ str(idx)
            if not os.path.exists(savepath):
                os.makedirs(savepath)
            #加载数据
            db_data = load_dataset(self.filepath, self.whichdb, name, args)
            #db_data = (db_data - min(db_data))/ (max(db_data) - min(db_data)) # 归一化
            #裁剪数据集
            
            i = 0
            foi = np.linspace(1, self.ylim, 100)
            faslt = SuperletTransform(  inputSize        = self.length,
                                        frequencyRange   = None, 
                                        frequencyBins    = None, 
                                        samplingRate     = self.fs, 
                                        frequencies      = foi, 
                                        baseCycles       = 5, 
                                        superletOrders   = (5,5))
            index = 1
            num = (args.fs * 4- args.length) / args.movement + 1
            if num > args.plg_num:
                sum = self.movement * self.plg_num
            else:
                sum = self.movement * (self.plg_num + 3)
            while i< sum:
                if self.whichdb == 'PBU' and index % (num+1) ==0:
                    i += self.movement
                data = db_data[i:i+self.length]
                i += self.movement  # 移动
                img_mtx = faslt.transform(data)
                plt.imshow(img_mtx, cmap="jet", aspect="auto", origin='lower')
                plt.savefig('{}/{}.jpeg'.format(savepath, index))
                plt.clf()   # 清空当前figure内容
                index += 1

    def plt_gadf(self):
        #设置绘制图形参数
        plt.axis('off')                           #关闭坐标轴
        plt.figure(figsize=(2.2, 2.2), dpi=100)   # 设置图片分辨率220*220
        plt.subplots_adjust(top=1, bottom=0, right=1, left=0,hspace=0, wspace=0)  # 边界与画布重合
        plt.margins(0, 0)  # 设置图形边距

        for name in self.datanames:
           #创建文件保存路径
            savepath = self.savepath +'/'+ name
            if not os.path.exists(savepath):
                os.makedirs(savepath)
            #加载数据
            db_data = load_dataset(self.filepath, self.whichdb, name, args)
            #db_data = (db_data - min(db_data))/ (max(db_data) - min(db_data)) # 归一化
            #裁剪数据集
            i = 0
            sum = self.movement * self.plg_num
            martx_data = []
            while i< sum:
                data = db_data[i:i+self.length]
                # 上包络谱
                #amp_env = np.abs( signal.hilbert(data) )
                # fft幅值谱
                #amp = abs(np.fft.fft(data))
                martx_data.append( data )
                i += self.movement  # 移动
            #绘制
            GADF = GramianAngularField(method='difference')
            data_GADF = GADF.transform(np.array(martx_data))
            #保存图片
            index = 1
            for img in data_GADF:
                plt.imshow(img, origin='lower')#  cmap='jet',vmin=-1., vmax=1, )
                plt.savefig('{}/{}.jpeg'.format(savepath, index))
                plt.clf()   # 清空当前figure内容
                index += 1

    def plt_gasf(self):
        #设置绘制图形参数
        plt.axis('off')                           #关闭坐标轴
        plt.figure(figsize=(2.2, 2.2), dpi=100)   # 设置图片分辨率220*220
        plt.subplots_adjust(top=1, bottom=0, right=1, left=0,hspace=0, wspace=0)  # 边界与画布重合
        plt.margins(0, 0)  # 设置图形边距

        for name in self.datanames:
           #创建文件保存路径
            savepath = self.savepath + '/' + name
            if not os.path.exists(savepath):
                os.makedirs(savepath)
            #加载数据
            db_data = load_dataset(self.filepath, self.whichdb, name, args)
            #db_data = (db_data - min(db_data))/ (max(db_data) - min(db_data)) # 归一化
            #裁剪数据集
            i = 0
            sum = self.movement * self.plg_num
            martx_data = []
            while i< sum:
                data = db_data[i:i+self.length]
                #downsampled_data = signal.decimate(data, 12000//6000)
                martx_data.append( data )
                i += self.movement  # 移动
            #绘制
            GASF = GramianAngularField( image_size=self.length//2, method='summation', overlapping=True)
            data_GASF = GASF.transform(np.array(martx_data))
            #保存图片
            index = 1
            for img in data_GASF:
                coeffs = pywt.dwt2(img, 'haar')
                cA, (cH, cV, cD) = coeffs
                plt.imshow(cA, origin='lower', cmap='rainbow')#   cmap='jet',vmin=-1., vmax=1, )
                plt.savefig('{}/{}.jpeg'.format(savepath, index))
                plt.clf()
                index += 1

    def plt_mtf(self):
        #设置绘制图形参数
        plt.axis('off')                           #关闭坐标轴
        plt.figure(figsize=(2.2, 2.2), dpi=100)   # 设置图片分辨率220*220
        plt.subplots_adjust(top=1, bottom=0, right=1, left=0,hspace=0, wspace=0)  # 边界与画布重合
        plt.margins(0, 0)  # 设置图形边距

        for name in self.datanames:
           #创建文件保存路径
            savepath = self.savepath + '/' + name
            if not os.path.exists(savepath):
                os.makedirs(savepath)
            #加载数据
            db_data = load_dataset(self.filepath, self.whichdb, name, args)
            #db_data = (db_data - min(db_data))/ (max(db_data) - min(db_data)) # 归一化
            #裁剪数据集
            i = 0
            sum = self.movement * self.plg_num
            martx_data = []
            while i< sum:
                martx_data.append( db_data[i:i+self.length] )
                i += self.movement  # 移动
            #绘制
            MTF = MarkovTransitionField()
            data_MTF = MTF.transform(np.array(martx_data))
            #保存图片
            index = 1
            for plt_data in data_MTF:
                plt.imshow(plt_data, origin='lower', cmap='rainbow')
                plt.savefig('{}/{}.jpeg'.format(savepath, index))
                plt.clf()
                index += 1

    
if __name__ == "__main__":
    args = get_parser()
    print(args)
    plt_sample = plt_2d_dataset(args)
    if args.plg_type == 'GADF':
        plt_sample.plt_gadf()
    elif args.plg_type == 'GASF':
        plt_sample.plt_gasf()
    elif args.plg_type == 'CWT':
        plt_sample.plt_cwt()
    elif args.plg_type == 'MTF':
        plt_sample.plt_mtf()
    elif args.plg_type == 'SLT':
        plt_sample.plt_superlet()
    elif args.plg_type == 'STFT':
        plt_sample.plt_stft()
