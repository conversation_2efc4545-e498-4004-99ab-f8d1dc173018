Namespace(config='configyaml/ablation_wtconvnextv2_atto_wavelet_noema.yaml', seed=1, num_workers=0, model_name='wtconvnextv2_atto', model_dir='DeepLearn_Fault/checkpoint/convnextv2_atto/convnextv2_atto_1k_224_ema.pt', use_wavelet=True, use_ema=False, data_dir='./datasets/img_dataset_split', train_dataset='A_SLT', test_dataset=None, train='train', vali='val', test='test', is_training=1, batch_size=16, n_epoch=30, early_stop=20, epoch_based_training=False, lr=0.0005, momentum=0.9, weight_decay=1e-05, lr_gamma=0.0003, lr_decay=0.75, lr_scheduler=True, t_sne=1, confmtx=1, device=device(type='cuda'))
{0: '0', 1: '1', 2: '2', 3: '3', 4: '4', 5: '5', 6: '6', 7: '7', 8: '8', 9: '9'}
{0: '0', 1: '1', 2: '2', 3: '3', 4: '4', 5: '5', 6: '6', 7: '7', 8: '8', 9: '9'}
{0: '0', 1: '1', 2: '2', 3: '3', 4: '4', 5: '5', 6: '6', 7: '7', 8: '8', 9: '9'}
ConvNeXtV2 配置: use_wavelet=True, use_ema=False
ModuleList(
  (0): Sequential(
    (0): Conv2d(3, 40, kernel_size=(4, 4), stride=(4, 4))
    (1): LayerNorm()
  )
  (1): Sequential(
    (0): LayerNorm()
    (1): Conv2d(40, 80, kernel_size=(2, 2), stride=(2, 2))
  )
  (2): Sequential(
    (0): LayerNorm()
    (1): Conv2d(80, 160, kernel_size=(2, 2), stride=(2, 2))
  )
  (3): Sequential(
    (0): LayerNorm()
    (1): Conv2d(160, 320, kernel_size=(2, 2), stride=(2, 2))
  )
)
ModuleList(
  (0): Sequential(
    (0): Block(
      (dwconv): WTConv2d(
        (base_conv): Conv2d(40, 40, kernel_size=(7, 7), stride=(1, 1), padding=same, groups=40)
        (base_scale): _ScaleModule()
        (wavelet_convs): ModuleList(
          (0): Conv2d(160, 160, kernel_size=(7, 7), stride=(1, 1), padding=same, groups=160, bias=False)
        )
        (wavelet_scale): ModuleList(
          (0): _ScaleModule()
        )
      )
      (norm): LayerNorm()
      (pwconv1): Linear(in_features=40, out_features=160, bias=True)
      (act): GELU(approximate='none')
      (grn): GRN()
      (pwconv2): Linear(in_features=160, out_features=40, bias=True)
      (drop_path): Identity()
    )
    (1): Block(
      (dwconv): WTConv2d(
        (base_conv): Conv2d(40, 40, kernel_size=(7, 7), stride=(1, 1), padding=same, groups=40)
        (base_scale): _ScaleModule()
        (wavelet_convs): ModuleList(
          (0): Conv2d(160, 160, kernel_size=(7, 7), stride=(1, 1), padding=same, groups=160, bias=False)
        )
        (wavelet_scale): ModuleList(
          (0): _ScaleModule()
        )
      )
      (norm): LayerNorm()
      (pwconv1): Linear(in_features=40, out_features=160, bias=True)
      (act): GELU(approximate='none')
      (grn): GRN()
      (pwconv2): Linear(in_features=160, out_features=40, bias=True)
      (drop_path): Identity()
    )
  )
  (1): Sequential(
    (0): Block(
      (dwconv): WTConv2d(
        (base_conv): Conv2d(80, 80, kernel_size=(7, 7), stride=(1, 1), padding=same, groups=80)
        (base_scale): _ScaleModule()
        (wavelet_convs): ModuleList(
          (0): Conv2d(320, 320, kernel_size=(7, 7), stride=(1, 1), padding=same, groups=320, bias=False)
        )
        (wavelet_scale): ModuleList(
          (0): _ScaleModule()
        )
      )
      (norm): LayerNorm()
      (pwconv1): Linear(in_features=80, out_features=320, bias=True)
      (act): GELU(approximate='none')
      (grn): GRN()
      (pwconv2): Linear(in_features=320, out_features=80, bias=True)
      (drop_path): Identity()
    )
    (1): Block(
      (dwconv): WTConv2d(
        (base_conv): Conv2d(80, 80, kernel_size=(7, 7), stride=(1, 1), padding=same, groups=80)
        (base_scale): _ScaleModule()
        (wavelet_convs): ModuleList(
          (0): Conv2d(320, 320, kernel_size=(7, 7), stride=(1, 1), padding=same, groups=320, bias=False)
        )
        (wavelet_scale): ModuleList(
          (0): _ScaleModule()
        )
      )
      (norm): LayerNorm()
      (pwconv1): Linear(in_features=80, out_features=320, bias=True)
      (act): GELU(approximate='none')
      (grn): GRN()
      (pwconv2): Linear(in_features=320, out_features=80, bias=True)
      (drop_path): Identity()
    )
  )
  (2): Sequential(
    (0): Block(
      (dwconv): WTConv2d(
        (base_conv): Conv2d(160, 160, kernel_size=(7, 7), stride=(1, 1), padding=same, groups=160)
        (base_scale): _ScaleModule()
        (wavelet_convs): ModuleList(
          (0): Conv2d(640, 640, kernel_size=(7, 7), stride=(1, 1), padding=same, groups=640, bias=False)
        )
        (wavelet_scale): ModuleList(
          (0): _ScaleModule()
        )
      )
      (norm): LayerNorm()
      (pwconv1): Linear(in_features=160, out_features=640, bias=True)
      (act): GELU(approximate='none')
      (grn): GRN()
      (pwconv2): Linear(in_features=640, out_features=160, bias=True)
      (drop_path): Identity()
    )
    (1): Block(
      (dwconv): WTConv2d(
        (base_conv): Conv2d(160, 160, kernel_size=(7, 7), stride=(1, 1), padding=same, groups=160)
        (base_scale): _ScaleModule()
        (wavelet_convs): ModuleList(
          (0): Conv2d(640, 640, kernel_size=(7, 7), stride=(1, 1), padding=same, groups=640, bias=False)
        )
        (wavelet_scale): ModuleList(
          (0): _ScaleModule()
        )
      )
      (norm): LayerNorm()
      (pwconv1): Linear(in_features=160, out_features=640, bias=True)
      (act): GELU(approximate='none')
      (grn): GRN()
      (pwconv2): Linear(in_features=640, out_features=160, bias=True)
      (drop_path): Identity()
    )
    (2): Block(
      (dwconv): WTConv2d(
        (base_conv): Conv2d(160, 160, kernel_size=(7, 7), stride=(1, 1), padding=same, groups=160)
        (base_scale): _ScaleModule()
        (wavelet_convs): ModuleList(
          (0): Conv2d(640, 640, kernel_size=(7, 7), stride=(1, 1), padding=same, groups=640, bias=False)
        )
        (wavelet_scale): ModuleList(
          (0): _ScaleModule()
        )
      )
      (norm): LayerNorm()
      (pwconv1): Linear(in_features=160, out_features=640, bias=True)
      (act): GELU(approximate='none')
      (grn): GRN()
      (pwconv2): Linear(in_features=640, out_features=160, bias=True)
      (drop_path): Identity()
    )
    (3): Block(
      (dwconv): WTConv2d(
        (base_conv): Conv2d(160, 160, kernel_size=(7, 7), stride=(1, 1), padding=same, groups=160)
        (base_scale): _ScaleModule()
        (wavelet_convs): ModuleList(
          (0): Conv2d(640, 640, kernel_size=(7, 7), stride=(1, 1), padding=same, groups=640, bias=False)
        )
        (wavelet_scale): ModuleList(
          (0): _ScaleModule()
        )
      )
      (norm): LayerNorm()
      (pwconv1): Linear(in_features=160, out_features=640, bias=True)
      (act): GELU(approximate='none')
      (grn): GRN()
      (pwconv2): Linear(in_features=640, out_features=160, bias=True)
      (drop_path): Identity()
    )
    (4): Block(
      (dwconv): WTConv2d(
        (base_conv): Conv2d(160, 160, kernel_size=(7, 7), stride=(1, 1), padding=same, groups=160)
        (base_scale): _ScaleModule()
        (wavelet_convs): ModuleList(
          (0): Conv2d(640, 640, kernel_size=(7, 7), stride=(1, 1), padding=same, groups=640, bias=False)
        )
        (wavelet_scale): ModuleList(
          (0): _ScaleModule()
        )
      )
      (norm): LayerNorm()
      (pwconv1): Linear(in_features=160, out_features=640, bias=True)
      (act): GELU(approximate='none')
      (grn): GRN()
      (pwconv2): Linear(in_features=640, out_features=160, bias=True)
      (drop_path): Identity()
    )
    (5): Block(
      (dwconv): WTConv2d(
        (base_conv): Conv2d(160, 160, kernel_size=(7, 7), stride=(1, 1), padding=same, groups=160)
        (base_scale): _ScaleModule()
        (wavelet_convs): ModuleList(
          (0): Conv2d(640, 640, kernel_size=(7, 7), stride=(1, 1), padding=same, groups=640, bias=False)
        )
        (wavelet_scale): ModuleList(
          (0): _ScaleModule()
        )
      )
      (norm): LayerNorm()
      (pwconv1): Linear(in_features=160, out_features=640, bias=True)
      (act): GELU(approximate='none')
      (grn): GRN()
      (pwconv2): Linear(in_features=640, out_features=160, bias=True)
      (drop_path): Identity()
    )
  )
  (3): Sequential(
    (0): Block(
      (dwconv): WTConv2d(
        (base_conv): Conv2d(320, 320, kernel_size=(7, 7), stride=(1, 1), padding=same, groups=320)
        (base_scale): _ScaleModule()
        (wavelet_convs): ModuleList(
          (0): Conv2d(1280, 1280, kernel_size=(7, 7), stride=(1, 1), padding=same, groups=1280, bias=False)
        )
        (wavelet_scale): ModuleList(
          (0): _ScaleModule()
        )
      )
      (norm): LayerNorm()
      (pwconv1): Linear(in_features=320, out_features=1280, bias=True)
      (act): GELU(approximate='none')
      (grn): GRN()
      (pwconv2): Linear(in_features=1280, out_features=320, bias=True)
      (drop_path): Identity()
    )
    (1): Block(
      (dwconv): WTConv2d(
        (base_conv): Conv2d(320, 320, kernel_size=(7, 7), stride=(1, 1), padding=same, groups=320)
        (base_scale): _ScaleModule()
        (wavelet_convs): ModuleList(
          (0): Conv2d(1280, 1280, kernel_size=(7, 7), stride=(1, 1), padding=same, groups=1280, bias=False)
        )
        (wavelet_scale): ModuleList(
          (0): _ScaleModule()
        )
      )
      (norm): LayerNorm()
      (pwconv1): Linear(in_features=320, out_features=1280, bias=True)
      (act): GELU(approximate='none')
      (grn): GRN()
      (pwconv2): Linear(in_features=1280, out_features=320, bias=True)
      (drop_path): Identity()
    )
  )
)
LayerNorm((320,), eps=1e-06, elementwise_affine=True)
Linear(in_features=320, out_features=10, bias=True)
模型结构:
ConvNeXtV2(
  (downsample_layers): ModuleList(
    (0): Sequential(
      (0): Conv2d(3, 40, kernel_size=(4, 4), stride=(4, 4))
      (1): LayerNorm()
    )
    (1): Sequential(
      (0): LayerNorm()
      (1): Conv2d(40, 80, kernel_size=(2, 2), stride=(2, 2))
    )
    (2): Sequential(
      (0): LayerNorm()
      (1): Conv2d(80, 160, kernel_size=(2, 2), stride=(2, 2))
    )
    (3): Sequential(
      (0): LayerNorm()
      (1): Conv2d(160, 320, kernel_size=(2, 2), stride=(2, 2))
    )
  )
  (stages): ModuleList(
    (0): Sequential(
      (0): Block(
        (dwconv): WTConv2d(
          (base_conv): Conv2d(40, 40, kernel_size=(7, 7), stride=(1, 1), padding=same, groups=40)
          (base_scale): _ScaleModule()
          (wavelet_convs): ModuleList(
            (0): Conv2d(160, 160, kernel_size=(7, 7), stride=(1, 1), padding=same, groups=160, bias=False)
          )
          (wavelet_scale): ModuleList(
            (0): _ScaleModule()
          )
        )
        (norm): LayerNorm()
        (pwconv1): Linear(in_features=40, out_features=160, bias=True)
        (act): GELU(approximate='none')
        (grn): GRN()
        (pwconv2): Linear(in_features=160, out_features=40, bias=True)
        (drop_path): Identity()
      )
      (1): Block(
        (dwconv): WTConv2d(
          (base_conv): Conv2d(40, 40, kernel_size=(7, 7), stride=(1, 1), padding=same, groups=40)
          (base_scale): _ScaleModule()
          (wavelet_convs): ModuleList(
            (0): Conv2d(160, 160, kernel_size=(7, 7), stride=(1, 1), padding=same, groups=160, bias=False)
          )
          (wavelet_scale): ModuleList(
            (0): _ScaleModule()
          )
        )
        (norm): LayerNorm()
        (pwconv1): Linear(in_features=40, out_features=160, bias=True)
        (act): GELU(approximate='none')
        (grn): GRN()
        (pwconv2): Linear(in_features=160, out_features=40, bias=True)
        (drop_path): Identity()
      )
    )
    (1): Sequential(
      (0): Block(
        (dwconv): WTConv2d(
          (base_conv): Conv2d(80, 80, kernel_size=(7, 7), stride=(1, 1), padding=same, groups=80)
          (base_scale): _ScaleModule()
          (wavelet_convs): ModuleList(
            (0): Conv2d(320, 320, kernel_size=(7, 7), stride=(1, 1), padding=same, groups=320, bias=False)
          )
          (wavelet_scale): ModuleList(
            (0): _ScaleModule()
          )
        )
        (norm): LayerNorm()
        (pwconv1): Linear(in_features=80, out_features=320, bias=True)
        (act): GELU(approximate='none')
        (grn): GRN()
        (pwconv2): Linear(in_features=320, out_features=80, bias=True)
        (drop_path): Identity()
      )
      (1): Block(
        (dwconv): WTConv2d(
          (base_conv): Conv2d(80, 80, kernel_size=(7, 7), stride=(1, 1), padding=same, groups=80)
          (base_scale): _ScaleModule()
          (wavelet_convs): ModuleList(
            (0): Conv2d(320, 320, kernel_size=(7, 7), stride=(1, 1), padding=same, groups=320, bias=False)
          )
          (wavelet_scale): ModuleList(
            (0): _ScaleModule()
          )
        )
        (norm): LayerNorm()
        (pwconv1): Linear(in_features=80, out_features=320, bias=True)
        (act): GELU(approximate='none')
        (grn): GRN()
        (pwconv2): Linear(in_features=320, out_features=80, bias=True)
        (drop_path): Identity()
      )
    )
    (2): Sequential(
      (0): Block(
        (dwconv): WTConv2d(
          (base_conv): Conv2d(160, 160, kernel_size=(7, 7), stride=(1, 1), padding=same, groups=160)
          (base_scale): _ScaleModule()
          (wavelet_convs): ModuleList(
            (0): Conv2d(640, 640, kernel_size=(7, 7), stride=(1, 1), padding=same, groups=640, bias=False)
          )
          (wavelet_scale): ModuleList(
            (0): _ScaleModule()
          )
        )
        (norm): LayerNorm()
        (pwconv1): Linear(in_features=160, out_features=640, bias=True)
        (act): GELU(approximate='none')
        (grn): GRN()
        (pwconv2): Linear(in_features=640, out_features=160, bias=True)
        (drop_path): Identity()
      )
      (1): Block(
        (dwconv): WTConv2d(
          (base_conv): Conv2d(160, 160, kernel_size=(7, 7), stride=(1, 1), padding=same, groups=160)
          (base_scale): _ScaleModule()
          (wavelet_convs): ModuleList(
            (0): Conv2d(640, 640, kernel_size=(7, 7), stride=(1, 1), padding=same, groups=640, bias=False)
          )
          (wavelet_scale): ModuleList(
            (0): _ScaleModule()
          )
        )
        (norm): LayerNorm()
        (pwconv1): Linear(in_features=160, out_features=640, bias=True)
        (act): GELU(approximate='none')
        (grn): GRN()
        (pwconv2): Linear(in_features=640, out_features=160, bias=True)
        (drop_path): Identity()
      )
      (2): Block(
        (dwconv): WTConv2d(
          (base_conv): Conv2d(160, 160, kernel_size=(7, 7), stride=(1, 1), padding=same, groups=160)
          (base_scale): _ScaleModule()
          (wavelet_convs): ModuleList(
            (0): Conv2d(640, 640, kernel_size=(7, 7), stride=(1, 1), padding=same, groups=640, bias=False)
          )
          (wavelet_scale): ModuleList(
            (0): _ScaleModule()
          )
        )
        (norm): LayerNorm()
        (pwconv1): Linear(in_features=160, out_features=640, bias=True)
        (act): GELU(approximate='none')
        (grn): GRN()
        (pwconv2): Linear(in_features=640, out_features=160, bias=True)
        (drop_path): Identity()
      )
      (3): Block(
        (dwconv): WTConv2d(
          (base_conv): Conv2d(160, 160, kernel_size=(7, 7), stride=(1, 1), padding=same, groups=160)
          (base_scale): _ScaleModule()
          (wavelet_convs): ModuleList(
            (0): Conv2d(640, 640, kernel_size=(7, 7), stride=(1, 1), padding=same, groups=640, bias=False)
          )
          (wavelet_scale): ModuleList(
            (0): _ScaleModule()
          )
        )
        (norm): LayerNorm()
        (pwconv1): Linear(in_features=160, out_features=640, bias=True)
        (act): GELU(approximate='none')
        (grn): GRN()
        (pwconv2): Linear(in_features=640, out_features=160, bias=True)
        (drop_path): Identity()
      )
      (4): Block(
        (dwconv): WTConv2d(
          (base_conv): Conv2d(160, 160, kernel_size=(7, 7), stride=(1, 1), padding=same, groups=160)
          (base_scale): _ScaleModule()
          (wavelet_convs): ModuleList(
            (0): Conv2d(640, 640, kernel_size=(7, 7), stride=(1, 1), padding=same, groups=640, bias=False)
          )
          (wavelet_scale): ModuleList(
            (0): _ScaleModule()
          )
        )
        (norm): LayerNorm()
        (pwconv1): Linear(in_features=160, out_features=640, bias=True)
        (act): GELU(approximate='none')
        (grn): GRN()
        (pwconv2): Linear(in_features=640, out_features=160, bias=True)
        (drop_path): Identity()
      )
      (5): Block(
        (dwconv): WTConv2d(
          (base_conv): Conv2d(160, 160, kernel_size=(7, 7), stride=(1, 1), padding=same, groups=160)
          (base_scale): _ScaleModule()
          (wavelet_convs): ModuleList(
            (0): Conv2d(640, 640, kernel_size=(7, 7), stride=(1, 1), padding=same, groups=640, bias=False)
          )
          (wavelet_scale): ModuleList(
            (0): _ScaleModule()
          )
        )
        (norm): LayerNorm()
        (pwconv1): Linear(in_features=160, out_features=640, bias=True)
        (act): GELU(approximate='none')
        (grn): GRN()
        (pwconv2): Linear(in_features=640, out_features=160, bias=True)
        (drop_path): Identity()
      )
    )
    (3): Sequential(
      (0): Block(
        (dwconv): WTConv2d(
          (base_conv): Conv2d(320, 320, kernel_size=(7, 7), stride=(1, 1), padding=same, groups=320)
          (base_scale): _ScaleModule()
          (wavelet_convs): ModuleList(
            (0): Conv2d(1280, 1280, kernel_size=(7, 7), stride=(1, 1), padding=same, groups=1280, bias=False)
          )
          (wavelet_scale): ModuleList(
            (0): _ScaleModule()
          )
        )
        (norm): LayerNorm()
        (pwconv1): Linear(in_features=320, out_features=1280, bias=True)
        (act): GELU(approximate='none')
        (grn): GRN()
        (pwconv2): Linear(in_features=1280, out_features=320, bias=True)
        (drop_path): Identity()
      )
      (1): Block(
        (dwconv): WTConv2d(
          (base_conv): Conv2d(320, 320, kernel_size=(7, 7), stride=(1, 1), padding=same, groups=320)
          (base_scale): _ScaleModule()
          (wavelet_convs): ModuleList(
            (0): Conv2d(1280, 1280, kernel_size=(7, 7), stride=(1, 1), padding=same, groups=1280, bias=False)
          )
          (wavelet_scale): ModuleList(
            (0): _ScaleModule()
          )
        )
        (norm): LayerNorm()
        (pwconv1): Linear(in_features=320, out_features=1280, bias=True)
        (act): GELU(approximate='none')
        (grn): GRN()
        (pwconv2): Linear(in_features=1280, out_features=320, bias=True)
        (drop_path): Identity()
      )
    )
  )
  (norm): LayerNorm((320,), eps=1e-06, elementwise_affine=True)
  (head): Linear(in_features=320, out_features=10, bias=True)
)
优化器: SGD (
Parameter Group 0
    dampening: 0
    differentiable: False
    foreach: None
    initial_lr: 0.0005
    lr: 0.0005
    maximize: False
    momentum: 0.9
    nesterov: False
    weight_decay: 1e-05
)
学习率: 0.0005
训练集大小: 1200
验证集大小: 400
测试集大小: 400
Epoch:[ 1/30],train_loss:2.3554 | vali_loss:2.312545,vali_acc:0.1000 | test_loss:2.311668,test_acc:0.1000 | lr: 0.000499

Epoch:[ 2/30],train_loss:2.3145 | vali_loss:2.219551,vali_acc:0.1400 | test_loss:2.217321,test_acc:0.1500 | lr: 0.000495

Epoch:[ 3/30],train_loss:2.1172 | vali_loss:2.313057,vali_acc:0.1000 | test_loss:2.314842,test_acc:0.1000 | lr: 0.000488

Epoch:[ 4/30],train_loss:1.7898 | vali_loss:1.573471,vali_acc:0.5050 | test_loss:1.584958,test_acc:0.5200 | lr: 0.000479

Epoch:[ 5/30],train_loss:1.3439 | vali_loss:1.609414,vali_acc:0.3250 | test_loss:1.650118,test_acc:0.2925 | lr: 0.000467

Epoch:[ 6/30],train_loss:1.1331 | vali_loss:1.474563,vali_acc:0.4125 | test_loss:1.519923,test_acc:0.3825 | lr: 0.000453

Epoch:[ 7/30],train_loss:0.9692 | vali_loss:1.033339,vali_acc:0.6150 | test_loss:1.078023,test_acc:0.6150 | lr: 0.000436

Epoch:[ 8/30],train_loss:0.8473 | vali_loss:1.145918,vali_acc:0.4600 | test_loss:1.164663,test_acc:0.4625 | lr: 0.000418

Epoch:[ 9/30],train_loss:0.6559 | vali_loss:0.712022,vali_acc:0.7875 | test_loss:0.751716,test_acc:0.7450 | lr: 0.000397

Epoch:[10/30],train_loss:0.5301 | vali_loss:0.564092,vali_acc:0.8125 | test_loss:0.621573,test_acc:0.7400 | lr: 0.000376

Epoch:[11/30],train_loss:0.4507 | vali_loss:0.399954,vali_acc:0.9000 | test_loss:0.425218,test_acc:0.8850 | lr: 0.000352

Epoch:[12/30],train_loss:0.4138 | vali_loss:0.524319,vali_acc:0.8350 | test_loss:0.562122,test_acc:0.8250 | lr: 0.000328

Epoch:[13/30],train_loss:0.3088 | vali_loss:0.339726,vali_acc:0.8675 | test_loss:0.403792,test_acc:0.8375 | lr: 0.000303

Epoch:[14/30],train_loss:0.2710 | vali_loss:0.493879,vali_acc:0.8100 | test_loss:0.536828,test_acc:0.7825 | lr: 0.000277

Epoch:[15/30],train_loss:0.2292 | vali_loss:0.529062,vali_acc:0.7900 | test_loss:0.554007,test_acc:0.7900 | lr: 0.000251

Epoch:[16/30],train_loss:0.2202 | vali_loss:0.986933,vali_acc:0.6925 | test_loss:0.950571,test_acc:0.6925 | lr: 0.000225

Epoch:[17/30],train_loss:0.1918 | vali_loss:0.379067,vali_acc:0.8650 | test_loss:0.412504,test_acc:0.8450 | lr: 0.000199

Epoch:[18/30],train_loss:0.1722 | vali_loss:0.713997,vali_acc:0.7100 | test_loss:0.702317,test_acc:0.7100 | lr: 0.000174

Epoch:[19/30],train_loss:0.1942 | vali_loss:0.304444,vali_acc:0.8900 | test_loss:0.351822,test_acc:0.8800 | lr: 0.000149

Epoch:[20/30],train_loss:0.1313 | vali_loss:0.299088,vali_acc:0.8850 | test_loss:0.328870,test_acc:0.8900 | lr: 0.000126

Epoch:[21/30],train_loss:0.1252 | vali_loss:0.279352,vali_acc:0.9050 | test_loss:0.322053,test_acc:0.8950 | lr: 0.000104

Epoch:[22/30],train_loss:0.1191 | vali_loss:0.222294,vali_acc:0.9400 | test_loss:0.255091,test_acc:0.9200 | lr: 0.000084

Epoch:[23/30],train_loss:0.1146 | vali_loss:0.428227,vali_acc:0.8300 | test_loss:0.435227,test_acc:0.8250 | lr: 0.000065

Epoch:[24/30],train_loss:0.1059 | vali_loss:0.283753,vali_acc:0.9025 | test_loss:0.310727,test_acc:0.9025 | lr: 0.000049

Epoch:[25/30],train_loss:0.1035 | vali_loss:0.429605,vali_acc:0.8300 | test_loss:0.436068,test_acc:0.8325 | lr: 0.000035

Epoch:[26/30],train_loss:0.1001 | vali_loss:0.343534,vali_acc:0.8700 | test_loss:0.363598,test_acc:0.8725 | lr: 0.000023

Epoch:[27/30],train_loss:0.0955 | vali_loss:0.287587,vali_acc:0.9075 | test_loss:0.312498,test_acc:0.8900 | lr: 0.000013

Epoch:[28/30],train_loss:0.0942 | vali_loss:0.324585,vali_acc:0.8775 | test_loss:0.340456,test_acc:0.8850 | lr: 0.000007

Epoch:[29/30],train_loss:0.0883 | vali_loss:0.295208,vali_acc:0.8925 | test_loss:0.315433,test_acc:0.8975 | lr: 0.000002

Epoch:[30/30],train_loss:0.0910 | vali_loss:0.291256,vali_acc:0.8925 | test_loss:0.311385,test_acc:0.9000 | lr: 0.000001

total cost time:830.0808944702148

Train best vali_acc:0.9400
test shape: torch.Size([400, 10]) torch.Size([400])
accuracy:0.9200
