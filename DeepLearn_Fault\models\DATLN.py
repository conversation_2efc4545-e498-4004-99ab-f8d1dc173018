import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.autograd.variable import *
import os
import numpy as np

class GradientReverseLayer(torch.autograd.Function):
    @staticmethod
    def forward(ctx, coeff, input):
        ctx.coeff = coeff
        return input

    @staticmethod
    def backward(ctx, grad_outputs):
        coeff = ctx.coeff
        return None, -coeff * grad_outputs

class GradientReverseModule(nn.Module):
    def __init__(self, scheduler):
        super(GradientReverseModule, self).__init__()
        self.scheduler = scheduler
        self.global_step = 0.0
        self.coeff = 0.0
        self.grl = GradientReverseLayer.apply
    
    def forward(self, x):
        self.coeff = self.scheduler(self.global_step)
        self.global_step += 1.0
        return self.grl(self.coeff, x)

def aToBSheduler(step, A, B, gamma=10, max_iter=10000):
    """从A值平滑过渡到B值的学习率调度器"""
    ans = A + (2.0 / (1 + np.exp(- gamma * step * 1.0 / max_iter)) - 1.0) * (B - A)
    return float(ans)

class Classifier(nn.Module):
    """分类器网络"""
    def __init__(self, in_dim, out_dim):
        super(Classifier, self).__init__()
        self.fc = nn.Linear(in_dim, out_dim)
        self.main = nn.Sequential(
            self.fc,
            nn.Softmax(dim=-1)
        )

    def forward(self, x):
        out = [x]
        for module in self.main.children():
            x = module(x)
            out.append(x)
        return out

class DomainDiscriminator(nn.Module):
    """域判别器网络"""
    def __init__(self, in_feature):
        super(DomainDiscriminator, self).__init__()
        self.ad_layer1 = nn.Linear(in_feature, 1024)
        self.ad_layer2 = nn.Linear(1024, 1024)
        self.ad_layer3 = nn.Linear(1024, 1)
        self.sigmoid = nn.Sigmoid()
        
        self.grl = GradientReverseModule(lambda step: aToBSheduler(step, 0.0, 1.0, gamma=10, max_iter=10000))

        self.main = nn.Sequential(
            self.ad_layer1,
            nn.BatchNorm1d(1024),
            nn.LeakyReLU(0.2, inplace=True),
            self.ad_layer2,
            nn.BatchNorm1d(1024),
            nn.LeakyReLU(0.2, inplace=True),
            self.ad_layer3,
            self.sigmoid
        )
    
    def forward(self, x):
        x = self.grl(x)
        for module in self.main.children():
            x = module(x)
        return x

class DATLN(nn.Module):
    """深度对抗迁移学习网络 (Deep Adversarial Transfer Learning Network)
    
    使用对抗训练来减小源域和目标域的分布差异
    """
    def __init__(self, feature_extractor, num_classes, feature_dim=320):
        super(DATLN, self).__init__()
        self.feature_extractor = feature_extractor
        
        # 移除特征提取器的分类头
        if hasattr(self.feature_extractor, 'head'):
            self.feature_extractor.head = nn.Identity()
        
        # 分类器
        self.classifier = Classifier(feature_dim, num_classes)
        
        # 域判别器
        self.domain_discriminator = DomainDiscriminator(feature_dim)
    
    def forward(self, x, domain='source'):
        # 提取特征
        features = self.feature_extractor(x)
        
        # 分类
        cls_out = self.classifier(features)
        
        # 域对抗
        domain_pred = self.domain_discriminator(features)
        
        return {
            'features': features,
            'logits': cls_out[1],  # softmax后的输出
            'domain_pred': domain_pred,
            'embeddings': cls_out[0]  # 分类器输入的特征
        }
    
    def get_features(self, x):
        """仅提取特征，用于t-SNE可视化等"""
        return self.feature_extractor(x)

def create_DATLN_model(model_name, num_classes, feature_extractor):
    """
    创建DATLN模型
    
    Args:
        model_name: 模型名称
        num_classes: 分类类别数
        feature_extractor: 特征提取器
    
    Returns:
        DATLN模型实例
    """
    # 获取特征维度，对于convnextv2_atto是320
    feature_dim = 320
    
    model = DATLN(feature_extractor, num_classes, feature_dim)
    
    # 打印创建的模型信息
    print(f"成功创建DATLN模型，特征提取器基于ConvNeXtV2 Atto，特征维度: {feature_dim}")
    print("注意：特征提取器的预训练权重已通过ConvNeXtV2_atto函数加载")
    
    return model
