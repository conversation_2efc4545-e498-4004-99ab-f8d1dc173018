"""
ConvNeXt V2 模型实现
基于 https://github.com/facebookresearch/ConvNeXt-V2 的官方实现
本文件包含 ConvNeXt V2 atto 和 tiny 模型，atto 是最轻量级的模型，tiny 是轻量级但性能更好的模型
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from timm.models.layers import trunc_normal_, DropPath
import os
import pywt
import pywt.data

# 小波变换相关函数
def create_wavelet_filter(wave, in_size, out_size, type=torch.float):
    w = pywt.Wavelet(wave)
    dec_hi = torch.tensor(w.dec_hi[::-1], dtype=type)
    dec_lo = torch.tensor(w.dec_lo[::-1], dtype=type)
    dec_filters = torch.stack([dec_lo.unsqueeze(0) * dec_lo.unsqueeze(1),
                           dec_lo.unsqueeze(0) * dec_hi.unsqueeze(1),
                           dec_hi.unsqueeze(0) * dec_lo.unsqueeze(1),
                           dec_hi.unsqueeze(0) * dec_hi.unsqueeze(1)], dim=0)

    dec_filters = dec_filters[:, None].repeat(in_size, 1, 1, 1)

    rec_hi = torch.tensor(w.rec_hi[::-1], dtype=type).flip(dims=[0])
    rec_lo = torch.tensor(w.rec_lo[::-1], dtype=type).flip(dims=[0])
    rec_filters = torch.stack([rec_lo.unsqueeze(0) * rec_lo.unsqueeze(1),
                           rec_lo.unsqueeze(0) * rec_hi.unsqueeze(1),
                           rec_hi.unsqueeze(0) * rec_lo.unsqueeze(1),
                           rec_hi.unsqueeze(0) * rec_hi.unsqueeze(1)], dim=0)

    rec_filters = rec_filters[:, None].repeat(out_size, 1, 1, 1)

    return dec_filters, rec_filters

def wavelet_transform(x, filters):
    b, c, h, w = x.shape
    pad = (filters.shape[2] // 2 - 1, filters.shape[3] // 2 - 1)
    x = F.conv2d(x, filters, stride=2, groups=c, padding=pad)
    x = x.reshape(b, c, 4, h // 2, w // 2)
    return x

def inverse_wavelet_transform(x, filters):
    b, c, _, h_half, w_half = x.shape
    pad = (filters.shape[2] // 2 - 1, filters.shape[3] // 2 - 1)
    x = x.reshape(b, c * 4, h_half, w_half)
    x = F.conv_transpose2d(x, filters, stride=2, groups=c, padding=pad)
    return x

# 小波卷积层
class _ScaleModule(nn.Module):
    def __init__(self, dims, init_scale=1.0, init_bias=0):
        super(_ScaleModule, self).__init__()
        self.dims = dims
        self.weight = nn.Parameter(torch.ones(*dims) * init_scale)
        self.bias = None

    def forward(self, x):
        return torch.mul(self.weight, x)

class WTConv2d(nn.Module):
    def __init__(self, in_channels, out_channels, kernel_size=5, stride=1, bias=True, wt_levels=1, wt_type='db1'):
        super(WTConv2d, self).__init__()

        assert in_channels == out_channels

        self.in_channels = in_channels
        self.wt_levels = wt_levels
        self.stride = stride
        self.dilation = 1

        self.wt_filter, self.iwt_filter = create_wavelet_filter(wt_type, in_channels, in_channels, torch.float)
        self.wt_filter = nn.Parameter(self.wt_filter, requires_grad=False)
        self.iwt_filter = nn.Parameter(self.iwt_filter, requires_grad=False)

        self.base_conv = nn.Conv2d(in_channels, in_channels, kernel_size, padding='same', stride=1, dilation=1, groups=in_channels, bias=bias)
        self.base_scale = _ScaleModule([1,in_channels,1,1])

        self.wavelet_convs = nn.ModuleList(
            [nn.Conv2d(in_channels*4, in_channels*4, kernel_size, padding='same', stride=1, dilation=1, groups=in_channels*4, bias=False) for _ in range(self.wt_levels)]
        )
        self.wavelet_scale = nn.ModuleList(
            [_ScaleModule([1,in_channels*4,1,1], init_scale=0.1) for _ in range(self.wt_levels)]
        )

        if self.stride > 1:
            self.do_stride = nn.AvgPool2d(kernel_size=1, stride=stride)
        else:
            self.do_stride = None

    def forward(self, x):

        x_ll_in_levels = []
        x_h_in_levels = []
        shapes_in_levels = []

        curr_x_ll = x

        for i in range(self.wt_levels):
            curr_shape = curr_x_ll.shape
            shapes_in_levels.append(curr_shape)
            if (curr_shape[2] % 2 > 0) or (curr_shape[3] % 2 > 0):
                curr_pads = (0, curr_shape[3] % 2, 0, curr_shape[2] % 2)
                curr_x_ll = F.pad(curr_x_ll, curr_pads)

            curr_x = wavelet_transform(curr_x_ll, self.wt_filter)
            curr_x_ll = curr_x[:,:,0,:,:]

            shape_x = curr_x.shape
            curr_x_tag = curr_x.reshape(shape_x[0], shape_x[1] * 4, shape_x[3], shape_x[4])
            curr_x_tag = self.wavelet_scale[i](self.wavelet_convs[i](curr_x_tag))
            curr_x_tag = curr_x_tag.reshape(shape_x)

            x_ll_in_levels.append(curr_x_tag[:,:,0,:,:])
            x_h_in_levels.append(curr_x_tag[:,:,1:4,:,:])

        next_x_ll = 0

        for i in range(self.wt_levels-1, -1, -1):
            curr_x_ll = x_ll_in_levels.pop()
            curr_x_h = x_h_in_levels.pop()
            curr_shape = shapes_in_levels.pop()

            curr_x_ll = curr_x_ll + next_x_ll

            curr_x = torch.cat([curr_x_ll.unsqueeze(2), curr_x_h], dim=2)
            next_x_ll = inverse_wavelet_transform(curr_x, self.iwt_filter)

            next_x_ll = next_x_ll[:, :, :curr_shape[2], :curr_shape[3]]

        x_tag = next_x_ll
        assert len(x_ll_in_levels) == 0

        x = self.base_scale(self.base_conv(x))
        x = x + x_tag

        if self.do_stride is not None:
            x = self.do_stride(x)

        return x

# 添加EMA注意力模块
class EMA(nn.Module):
    def __init__(self, channels, c2=None, factor=32):
        super(EMA, self).__init__()
        self.groups = factor
        assert channels // self.groups > 0
        self.softmax = nn.Softmax(-1)
        self.agp = nn.AdaptiveAvgPool2d((1, 1))
        self.pool_h = nn.AdaptiveAvgPool2d((None, 1))
        self.pool_w = nn.AdaptiveAvgPool2d((1, None))
        self.gn = nn.GroupNorm(channels // self.groups, channels // self.groups)
        self.conv1x1 = nn.Conv2d(channels // self.groups, channels // self.groups, kernel_size=1, stride=1, padding=0)
        self.conv3x3 = nn.Conv2d(channels // self.groups, channels // self.groups, kernel_size=3, stride=1, padding=1)

    def forward(self, x):
        b, c, h, w = x.size()
        group_x = x.reshape(b * self.groups, -1, h, w)  # b*g,c//g,h,w
        x_h = self.pool_h(group_x)
        x_w = self.pool_w(group_x).permute(0, 1, 3, 2)
        hw = self.conv1x1(torch.cat([x_h, x_w], dim=2))
        x_h, x_w = torch.split(hw, [h, w], dim=2)
        x1 = self.gn(group_x * x_h.sigmoid() * x_w.permute(0, 1, 3, 2).sigmoid())
        x2 = self.conv3x3(group_x)
        x11 = self.softmax(self.agp(x1).reshape(b * self.groups, -1, 1).permute(0, 2, 1))
        x12 = x2.reshape(b * self.groups, c // self.groups, -1)  # b*g, c//g, hw
        x21 = self.softmax(self.agp(x2).reshape(b * self.groups, -1, 1).permute(0, 2, 1))
        x22 = x1.reshape(b * self.groups, c // self.groups, -1)  # b*g, c//g, hw
        weights = (torch.matmul(x11, x12) + torch.matmul(x21, x22)).reshape(b * self.groups, 1, h, w)
        return (group_x * weights.sigmoid()).reshape(b, c, h, w)

class LayerNorm(nn.Module):
    """ LayerNorm 支持两种数据格式：channels_last（默认）或 channels_first
    输入的维度排列。channels_last 对应形状为 (batch_size, height, width, channels) 的输入，
    而 channels_first 对应形状为 (batch_size, channels, height, width) 的输入。
    """
    def __init__(self, normalized_shape, eps=1e-6, data_format="channels_last"):
        super().__init__()
        self.weight = nn.Parameter(torch.ones(normalized_shape))
        self.bias = nn.Parameter(torch.zeros(normalized_shape))
        self.eps = eps
        self.data_format = data_format
        if self.data_format not in ["channels_last", "channels_first"]:
            raise NotImplementedError
        self.normalized_shape = (normalized_shape, )

    def forward(self, x):
        if self.data_format == "channels_last":
            return F.layer_norm(x, self.normalized_shape, self.weight, self.bias, self.eps)
        elif self.data_format == "channels_first":
            u = x.mean(1, keepdim=True)
            s = (x - u).pow(2).mean(1, keepdim=True)
            x = (x - u) / torch.sqrt(s + self.eps)
            x = self.weight[:, None, None] * x + self.bias[:, None, None]
            return x

class GRN(nn.Module):
    """ GRN (Global Response Normalization) 层
    """
    def __init__(self, dim):
        super().__init__()
        self.gamma = nn.Parameter(torch.zeros(1, 1, 1, dim))
        self.beta = nn.Parameter(torch.zeros(1, 1, 1, dim))

    def forward(self, x):
        Gx = torch.norm(x, p=2, dim=(1,2), keepdim=True)
        Nx = Gx / (Gx.mean(dim=-1, keepdim=True) + 1e-6)
        return self.gamma * (x * Nx) + self.beta + x

class Block(nn.Module):
    """ ConvNeXtV2 Block.

    Args:
        dim (int): 输入通道数
        drop_path (float): 随机深度衰减率. Default: 0.0
        use_wavelet (bool): 是否使用小波卷积. Default: True
        use_ema (bool): 是否使用EMA注意力模块. Default: True
    """
    def __init__(self, dim, drop_path=0., use_wavelet=True, use_ema=True):
        super().__init__()
        # 根据use_wavelet参数决定使用小波卷积还是标准深度卷积
        if use_wavelet:
            self.dwconv = WTConv2d(dim, dim, kernel_size=7) # 小波深度卷积
        else:
            self.dwconv = nn.Conv2d(dim, dim, kernel_size=7, padding=3, groups=dim) # 标准深度卷积

        self.norm = LayerNorm(dim, eps=1e-6)
        self.pwconv1 = nn.Linear(dim, 4 * dim) # 点卷积/1x1 卷积，使用线性层实现
        self.act = nn.GELU()
        self.grn = GRN(4 * dim)

        # 根据use_ema参数决定是否使用EMA注意力模块
        self.use_ema = use_ema
        if use_ema:
            self.ema = EMA(4 * dim, factor=32)

        self.pwconv2 = nn.Linear(4 * dim, dim)
        self.drop_path = DropPath(drop_path) if drop_path > 0. else nn.Identity()

    def forward(self, x):
        input = x
        x = self.dwconv(x)
        x = x.permute(0, 2, 3, 1) # (N, C, H, W) -> (N, H, W, C)
        x = self.norm(x)
        x = self.pwconv1(x)
        x = self.act(x)
        x = self.grn(x)

        # 如果启用EMA，则应用EMA注意力
        if self.use_ema:
            # 在GRN之后、pwconv2之前添加EMA
            # 需要将x从(N, H, W, 4*C)转换为(N, 4*C, H, W)以应用EMA
            x_ema = x.permute(0, 3, 1, 2)  # (N, H, W, 4*C) -> (N, 4*C, H, W)
            x_ema = self.ema(x_ema)  # 应用EMA注意力
            x = x_ema.permute(0, 2, 3, 1)  # (N, 4*C, H, W) -> (N, H, W, 4*C)

        x = self.pwconv2(x)
        x = x.permute(0, 3, 1, 2) # (N, H, W, C) -> (N, C, H, W)

        x = input + self.drop_path(x)
        return x

class ConvNeXtV2(nn.Module):
    """ ConvNeXt V2

    Args:
        in_chans (int): 输入图像通道数. Default: 3
        num_classes (int): 分类头的类别数. Default: 1000
        depths (tuple(int)): 每个阶段的块数. Default: [3, 3, 9, 3]
        dims (int): 每个阶段的特征维度. Default: [96, 192, 384, 768]
        drop_path_rate (float): 随机深度衰减率. Default: 0.
        head_init_scale (float): 分类器权重和偏置的初始缩放值. Default: 1.
        use_wavelet (bool): 是否使用小波卷积. Default: True
        use_ema (bool): 是否使用EMA注意力模块. Default: True
    """
    def __init__(self, in_chans=3, num_classes=1000,
                 depths=[3, 3, 9, 3], dims=[96, 192, 384, 768],
                 drop_path_rate=0., head_init_scale=1.,
                 use_wavelet=True, use_ema=True
                 ):
        super().__init__()
        self.depths = depths
        self.use_wavelet = use_wavelet
        self.use_ema = use_ema

        # 记录配置信息，方便调试
        print(f"ConvNeXtV2 配置: use_wavelet={use_wavelet}, use_ema={use_ema}")

        self.downsample_layers = nn.ModuleList() # stem 和 3 个中间下采样卷积层
        # 注意：WTConv2d要求输入和输出通道数相同，所以这里仍然使用标准卷积
        stem = nn.Sequential(
            nn.Conv2d(in_chans, dims[0], kernel_size=4, stride=4),
            LayerNorm(dims[0], eps=1e-6, data_format="channels_first")
        )
        self.downsample_layers.append(stem)
        for i in range(3):
            downsample_layer = nn.Sequential(
                    LayerNorm(dims[i], eps=1e-6, data_format="channels_first"),
                    nn.Conv2d(dims[i], dims[i+1], kernel_size=2, stride=2),
            )
            self.downsample_layers.append(downsample_layer)

        self.stages = nn.ModuleList() # 4 个特征分辨率阶段，每个阶段包含多个残差块
        dp_rates=[x.item() for x in torch.linspace(0, drop_path_rate, sum(depths))]
        cur = 0
        for i in range(4):
            stage = nn.Sequential(
                *[Block(dim=dims[i], drop_path=dp_rates[cur + j],
                       use_wavelet=use_wavelet, use_ema=use_ema) for j in range(depths[i])]
            )
            self.stages.append(stage)
            cur += depths[i]

        self.norm = nn.LayerNorm(dims[-1], eps=1e-6) # 最终归一化层
        self.head = nn.Linear(dims[-1], num_classes)

        self.apply(self._init_weights)
        self.head.weight.data.mul_(head_init_scale)
        self.head.bias.data.mul_(head_init_scale)

    def _init_weights(self, m):
        if isinstance(m, (nn.Conv2d, nn.Linear)):
            trunc_normal_(m.weight, std=.02)
            if hasattr(m, 'bias') and m.bias is not None:
                nn.init.constant_(m.bias, 0)
        # WTConv2d有自己的初始化方式，不需要在这里初始化

    def forward_features(self, x):
        for i in range(4):
            x = self.downsample_layers[i](x)
            x = self.stages[i](x)
        return self.norm(x.mean([-2, -1])) # 全局平均池化, (N, C, H, W) -> (N, C)

    def forward(self, x):
        x = self.forward_features(x)
        x = self.head(x)
        return x

def convnextv2_atto(num_classes=1000, use_wavelet=True, use_ema=True, **kwargs):
    """
    构建 ConvNeXt V2 Atto 模型

    Args:
        num_classes (int): 分类类别数
        use_wavelet (bool): 是否使用小波卷积. Default: True
        use_ema (bool): 是否使用EMA注意力模块. Default: True
        **kwargs: 其他参数传递给 ConvNeXtV2 构造函数

    Returns:
        ConvNeXtV2 模型实例
    """
    model = ConvNeXtV2(
        depths=[2, 2, 6, 2],
        dims=[40, 80, 160, 320],
        num_classes=num_classes,
        use_wavelet=use_wavelet,
        use_ema=use_ema,
        **kwargs
    )
    return model

def convnextv2_tiny(num_classes=1000, pretrained=False, pretrained_path=None, **kwargs):
    """
    构建 ConvNeXt V2 Tiny 模型

    Args:
        num_classes (int): 分类类别数
        pretrained (bool): 是否使用预训练模型
        pretrained_path (str): 预训练模型路径
        **kwargs: 其他参数传递给 ConvNeXtV2 构造函数

    Returns:
        ConvNeXtV2 模型实例
    """
    model = ConvNeXtV2(
        depths=[3, 3, 9, 3],
        dims=[96, 192, 384, 768],
        num_classes=num_classes,
        **kwargs
    )

    if pretrained and pretrained_path:
        try:
            state_dict = torch.load(pretrained_path, map_location='cpu')
            model.load_state_dict(state_dict)
            print(f"成功加载预训练模型: {pretrained_path}")
        except Exception as e:
            print(f"加载预训练模型失败: {e}")
            print("将使用随机初始化的模型继续训练")

    return model

# 创建一个工厂函数，方便从model.py调用
def ConvNeXtV2_atto(model_name, num_classes, pretrained=False, pretrained_path=None, use_wavelet=True, use_ema=True):
    """
    返回 ConvNeXt V2 Atto 模型实例

    Args:
        model_name: 模型名称(仅用于兼容model.py的接口)
        num_classes: 分类类别数
        pretrained: 是否使用预训练模型
        pretrained_path: 预训练模型路径
        use_wavelet: 是否使用小波卷积
        use_ema: 是否使用EMA注意力模块

    Returns:
        ConvNeXtV2 Atto 模型实例
    """
    model = convnextv2_atto(num_classes=num_classes, use_wavelet=use_wavelet, use_ema=use_ema)

    # 如果没有提供预训练路径，但开启了pretrained选项，自动使用根目录的预训练模型
    if pretrained and not pretrained_path:
        # 先尝试使用小波卷积的预训练模型
        wt_pretrained_path = 'checkpoint/wtconvnextv2_atto/WTConvNeXt_tiny_5_300e_ema.pth'
        if os.path.exists(wt_pretrained_path):
            pretrained_path = wt_pretrained_path
            print(f"尝试使用小波卷积预训练模型: {pretrained_path}")
        else:
            pretrained_path = '../convnextv2_atto_1k_224_fcmae.pt'
            print(f"尝试使用默认预训练模型: {pretrained_path}")

    if pretrained and pretrained_path:
        print(f"开始加载ConvNeXtV2 Atto预训练模型: {pretrained_path}")
        try:
            if os.path.exists(pretrained_path):
                # 尝试加载状态字典
                try:
                    state_dict = torch.load(pretrained_path, map_location=torch.device('cpu'))
                except Exception as e:
                    print(f"加载预训练模型时出错: {e}")
                    print(f"尝试使用不同的加载方式...")
                    state_dict = torch.load(pretrained_path, map_location=torch.device('cpu'), weights_only=True)

                # 检查是否有'model'键，某些预训练模型（如fcmae格式）会将模型状态字典保存在'model'键下
                if isinstance(state_dict, dict) and 'model' in state_dict:
                    print("检测到预训练模型使用'model'键存储状态字典")
                    state_dict = state_dict['model']

                # 尝试加载状态字典，如果键不匹配，将进行部分加载
                model_dict = model.state_dict()

                # 处理头部不匹配的情况（预训练模型通常有1000类）
                if isinstance(state_dict, dict):
                    for k in list(state_dict.keys()):
                        if 'head' in k:
                            print(f"移除预训练头部参数: {k}")
                            state_dict.pop(k)

                    # 检查是否是小波卷积预训练模型
                    is_wt_pretrained = any('conv_dw.wt_filter' in k for k in state_dict.keys())
                else:
                    print(f"警告: 预训练模型不是字典格式，而是 {type(state_dict)}")
                    is_wt_pretrained = False

                if is_wt_pretrained:
                    print("检测到小波卷积预训练模型，将进行参数映射")
                    # 创建参数映射关系
                    param_mapping = {
                        'blocks': '',  # 小波模型使用blocks，而当前模型不使用
                        'conv_dw': 'dwconv',  # 小波模型使用conv_dw，而当前模型使用dwconv
                    }

                    # 创建新的状态字典
                    new_state_dict = {}
                    for k, v in state_dict.items():
                        # 将小波模型的键映射到当前模型的键
                        new_key = k
                        for old_str, new_str in param_mapping.items():
                            if old_str in new_key:
                                new_key = new_key.replace(old_str, new_str)

                        # 处理特殊情况，如stages.0.blocks.0 -> stages.0.0
                        parts = new_key.split('.')
                        if len(parts) > 3 and parts[0] == 'stages' and parts[2] == '':
                            parts[2] = parts[3]  # 将blocks后面的索引移到blocks的位置
                            parts.pop(3)  # 移除多余的索引
                            new_key = '.'.join(parts)

                        new_state_dict[new_key] = v

                    # 替换原始状态字典
                    state_dict = new_state_dict
                    print(f"小波卷积预训练模型参数映射完成，共{len(state_dict)}个参数")
                else:
                    # 处理深度卷积层到小波卷积层的参数转移
                    transferred_keys = []
                    for k in list(state_dict.keys()):
                        # 如果是深度卷积层的权重和偏置
                        if 'dwconv.weight' in k or 'dwconv.bias' in k:
                            stage_idx = int(k.split('.')[1])  # 获取stage索引
                            block_idx = int(k.split('.')[2])  # 获取block索引
                            param_type = k.split('.')[-1]  # weight或bias

                            # 构造小波卷积层的base_conv参数名称
                            new_key = f"stages.{stage_idx}.{block_idx}.dwconv.base_conv.{param_type}"

                            if new_key in model_dict:
                                # 将深度卷积层的参数转移到小波卷积层的base_conv中
                                state_dict[new_key] = state_dict[k]
                                print(f"将参数从 {k} 转移到 {new_key}")
                                transferred_keys.append(k)  # 记录已转移的键

                    # 将已转移的键从原始状态字典中移除，避免显示为未加载
                    for k in transferred_keys:
                        state_dict.pop(k)

                # 过滤掉不匹配的键
                if isinstance(state_dict, dict):
                    original_keys = list(state_dict.keys())
                    pretrained_dict = {k: v for k, v in state_dict.items() if k in model_dict and model_dict[k].shape == v.shape}

                    # 输出未加载的键
                    unloaded_keys = set(original_keys) - set(pretrained_dict.keys())
                    if unloaded_keys:
                        print(f"以下键未被加载 (共{len(unloaded_keys)}个):")
                        for k in sorted(list(unloaded_keys))[:10]:  # 只打印前10个
                            print(f"  - {k}")
                        if len(unloaded_keys) > 10:
                            print(f"  ... 以及其他 {len(unloaded_keys) - 10} 个键")

                    # 更新当前模型的字典
                    model_dict.update(pretrained_dict)
                    model.load_state_dict(model_dict)

                    print(f"成功加载ConvNeXtV2 Atto预训练模型: {pretrained_path}")
                    print(f"加载了 {len(pretrained_dict)}/{len(state_dict)} 层参数")
                else:
                    # 如果状态字典不是字典格式，尝试直接加载
                    try:
                        model.load_state_dict(state_dict)
                        print(f"成功直接加载ConvNeXtV2 Atto预训练模型: {pretrained_path}")
                    except Exception as e:
                        print(f"直接加载预训练模型失败: {e}")
                        print("将使用随机初始化的模型继续训练")
            else:
                print(f"预训练模型路径不存在: {pretrained_path}")
                print("将使用随机初始化的模型继续训练")
        except Exception as e:
            print(f"加载预训练模型失败: {e}")
            print(f"错误类型: {type(e).__name__}")
            print("将使用随机初始化的模型继续训练")

    return model

# 创建一个工厂函数，方便从model.py调用
def ConvNeXtV2_tiny(model_name, num_classes, pretrained=False, pretrained_path=None):
    """
    返回 ConvNeXt V2 Tiny 模型实例

    Args:
        model_name: 模型名称(仅用于兼容model.py的接口)
        num_classes: 分类类别数
        pretrained: 是否使用预训练模型
        pretrained_path: 预训练模型路径

    Returns:
        ConvNeXtV2 Tiny 模型实例
    """
    return convnextv2_tiny(num_classes=num_classes, pretrained=pretrained, pretrained_path=pretrained_path)