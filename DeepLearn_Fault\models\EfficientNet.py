import torch.nn as nn
from torchvision import models

efficientnet_dict = {
    "efficientnet_b4": models.efficientnet_b4,
    "efficientnet_b7": models.efficientnet_b7,
}

class EfficientNet(nn.Module):
    def __init__(self, network_type, num_classes):
        super(EfficientNet, self).__init__()
        efficientnet = efficientnet_dict[network_type](pretrained=True)
        in_features = efficientnet.classifier[1].in_features
        efficientnet.classifier[1] = nn.Linear(in_features, num_classes)
        self.model = efficientnet

    def forward(self, x):
        x = self.model(x)
        return x


            