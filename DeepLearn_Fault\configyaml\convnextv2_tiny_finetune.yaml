# ConvNeXt V2 Tiny 微调配置

# Model settings
model_name: convnextv2_tiny

# Model pretrained settings
model_settings:
  pretrained: true
  pretrained_path: checkpoint/pretrained_model/convnextv2_tiny_pretrained.pth

# Data settings
train_dataset: A_SLT
train: train
vali: val
test: test
data_dir: ./datasets/img_dataset_split

# Training settings
batch_size: 16
n_epoch: 20
early_stop: 20

# Optimizer settings
lr: 0.0001  # 微调时使用较小的学习率
momentum: 0.9
weight_decay: 1e-5  # 微调时可以使用较小的权重衰减

# Learning rate scheduler settings
lr_gamma: 0.0003
lr_decay: 0.75
lr_scheduler: true

# Confusion matrix usage
confmtx: 0

# t-SNE visualization
t_sne: 0 