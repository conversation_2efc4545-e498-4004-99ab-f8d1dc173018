# ConvNeXt V2 Atto Fine-tuning Configuration (FCMAE)

# Model settings
model_name: convnextv2_atto
model_dir: /root/lanyun-tmp/DeepLearn_Fault/convnextv2_atto_1k_224_.pt

# Dataset settings
train_dataset: A_SLT
train: train
vali: val
test: test
data_dir: ./datasets/img_dataset_split

# Training settings
batch_size:16
n_epoch: 50
early_stop: 10

# Optimizer settings
lr: 0.00005
momentum: 0.9
weight_decay: 1e-5

# LR scheduler settings
lr_gamma: 0.0003
lr_decay: 0.75
lr_scheduler: true

num_workers: 4
# Confusion matrix
confmtx: 0

# t-SNE visualization
t_sne: 0 