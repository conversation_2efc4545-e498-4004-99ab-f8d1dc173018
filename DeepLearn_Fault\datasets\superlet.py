# Time-frequency analysis with superlets
# Based on 'Time-frequency super-resolution with superlets'
# by <PERSON><PERSON> et al., 2021 Nature Communications
#
# Implementation by <PERSON> and <PERSON>

#
# Note: for runs on multiple batches of data, the class SuperletTransform can be instantiated just once
# this saves time and memory allocation for the wavelets and buffers
#


import numpy as np
from scipy.signal import fftconvolve

# spread, in units of standard deviation, of the Gaussian window of the Morlet wavelet
MORLET_SD_SPREAD = 6

# the length, in units of standard deviation, of the actual support window of the Morlet
MORLET_SD_FACTOR = 2.5



def computeWaveletSize(fc, nc, fs):
    """
    Compute the size in samples of a morlet wavelet.
    Arguments:
        fc - center frequency in Hz
        nc - number of cycles
        fs - sampling rate in Hz
    """
    sd = (nc / 2) * (1 / np.abs(fc)) / MORLET_SD_FACTOR
    return int(2 * np.floor(np.round(sd * fs * MORLET_SD_SPREAD) / 2) + 1)


def gausswin(size, alpha):
    """
    Create a Gaussian window.
    """
    halfSize    = int(np.floor(size / 2))
    idiv        = alpha / halfSize

    t = (np.array(range(size), dtype=np.float64) - halfSize) * idiv
    window = np.exp(-(t * t) * 0.5)
    
    return window

    

def morlet(fc, nc, fs):
    """
    Create an analytic Morlet wavelet.
    Arguments:
        fc - center frequency in Hz
        nc - number of cycles
        fs - sampling rate in Hz
    """
    size    = computeWaveletSize(fc, nc, fs)
    half    = int(np.floor(size / 2))
    gauss   = gausswin(size, MORLET_SD_SPREAD / 2)
    igsum   = 1 / gauss.sum()
    ifs     = 1 / fs

    t = (np.array(range(size), dtype=np.float64) - half) * ifs
    wavelet = gauss * np.exp(2 * np.pi * fc * t * 1j) * igsum

    return wavelet

def fractional(x):
    """
    Get the fractional part of the scalar value x.
    """
    return x - int(x)


class SuperletTransform:
    """
    Class used to compute the Superlet Transform of input data.
    """

    def __init__(   self,
                    inputSize,
                    samplingRate,
                    frequencyRange,
                    frequencyBins,
                    baseCycles,
                    superletOrders,
                    frequencies = None):
        """
        Initialize the superlet transform. 
        Arguments:
            inputSize: size of the input in samples
            samplingRate: the sampling rate of the input signal in Hz
            frequencyRange: tuplet of ascending frequency points, in Hz
            frequencyBins: number of frequency bins to sample in the interval frequencyRange
            baseCycles: number of cycles of the smallest wavelet (c1 in the paper)
            superletOrders: a tuple containing the range of superlet orders, linearly distributed along frequencyRange
            frequencies: specific list of frequencies - can be provided in stead of frequencyRange (it is ignored in this case)
        """
        # clear to reinit
        self.clear()

        # initialize containers
        if frequencies is not None:
            frequencyBins = len(frequencies)
            self.frequencies = frequencies
        else:
            self.frequencies = np.linspace(start=frequencyRange[0], stop=frequencyRange[1], num=frequencyBins)

        self.inputSize      = inputSize
        self.orders         = np.linspace(start=superletOrders[0], stop=superletOrders[1], num=frequencyBins)
        self.convBuffer     = np.zeros(inputSize, dtype=np.complex128)
        self.poolBuffer     = np.zeros(inputSize, dtype=np.float64)
        self.superlets      = []

        # create wavelets
        for iFreq in range(frequencyBins):
            centerFreq  = self.frequencies[iFreq]
            nWavelets   = int(np.ceil(self.orders[iFreq]))

            self.superlets.append([])
            for iWave in range(nWavelets):

                # create morlet wavelet
                self.superlets[iFreq].append(morlet(centerFreq, (iWave + 1) * baseCycles, samplingRate))


    def __del__(self):
        """
        Destructor.
        """
        self.clear()


    def clear(self):
        """
        Clear the transform.
        """
        # fields
        self.inputSize   = None
        self.superlets   = None
        self.poolBuffer  = None
        self.convBuffer  = None
        self.frequencies = None
        self.orders      = None


    
    def transform(self, inputData):
        """
        Apply the transform to a buffer or list of buffers.
        Arguments:
            inputData - an NDarray of input data
        """

        # compute number of arrays to transform
        if len(inputData.shape) == 1:
            if inputData.shape[0] != self.inputSize:
                raise "Input data must meet the defined input size for this transform."
            
            result = np.zeros((self.inputSize, len(self.frequencies)), dtype=np.float64)
            self.transformOne(inputData, result)
            return result

        else:
            n       = int(np.sum(inputData.shape[0:len(inputData.shape) - 1]))
            insize  = int(inputData.shape[len(inputData.shape) - 1])

            if insize != self.inputSize:
                raise "Input data must meet the defined input size for this transform."
            
            # reshape to data list
            datalist = np.reshape(inputData, (n, insize), 'C')
            result = np.zeros((len(self.frequencies), self.inputSize), dtype=np.float64)

            for i in range(0, n):
                self.transformOne(datalist[i, :], result)

            return result / n


    def transformOne(self, inputData, accumulator):
        """
        Apply the superlet transform on a single data buffer.
        Arguments:
            inputData: A 1xInputSize array containing the signal to be transformed.
            accumulator: a spectrum to accumulate the resulting superlet transform
        """
        accumulator.resize((len(self.frequencies), self.inputSize))

        for iFreq in range(len(self.frequencies)):
            
            # init pooling buffer
            self.poolBuffer.fill(1)

            if len(self.superlets[iFreq]) > 1:
                
                # superlet
                nWavelets   = int(np.floor(self.orders[iFreq]))
                rfactor     = 1.0 / nWavelets

                for iWave in range(nWavelets):
                    self.convBuffer = fftconvolve(inputData, self.superlets[iFreq][iWave], "same")
                    self.poolBuffer *= 2 * np.abs(self.convBuffer) ** 2

                if fractional(self.orders[iFreq]) != 0 and len(self.superlets[iFreq]) == nWavelets + 1:

                    # apply the fractional wavelet
                    exponent = self.orders[iFreq] - nWavelets
                    rfactor = 1 / (nWavelets + exponent)

                    self.convBuffer = fftconvolve(inputData, self.superlets[iFreq][nWavelets], "same")
                    self.poolBuffer *= (2 * np.abs(self.convBuffer) ** 2) ** exponent

                # perform geometric mean
                accumulator[iFreq, :] += self.poolBuffer ** rfactor


            else:
                # wavelet transform
                accumulator[iFreq, :] += (2 * np.abs(fftconvolve(inputData, self.superlets[iFreq][0], "same")) ** 2).astype(np.float64)


# main superlet function
def superlets(data,
              fs,
              foi,
              c1,
              ord):
    """
    Perform fractional adaptive superlet transform (FASLT) on a list of trials. 
    Arguments:
        data: a numpy array of data. The rightmost dimension of the data is the trial size. The result will be the average over all the spectra.
        fs: the sampling rate in Hz
        foi: list of frequencies of interest
        c1: base number of cycles parameter
        ord: the order (for SLT) or order range (for FASLT), spanned across the frequencies of interest
    Returns: a matrix containing the average superlet spectrum
    """
    # determine buffer size
    bufferSize = data.shape[len(data.shape) - 1]
    # make order parameter
    if len(ord) == 1:
        ord = (ord, ord)

    # build the superlet analyzer
    faslt = SuperletTransform(  inputSize        = bufferSize, 
                                frequencyRange   = None, 
                                frequencyBins    = None, 
                                samplingRate     = fs, 
                                frequencies      = foi, 
                                baseCycles       = c1, 
                                superletOrders   = ord)

    # perform transform
    return faslt.transform(data)

def multi_scale_superlets(data, fs, scales, base_cycles_list, order_ranges, freq_ranges=None):
    """
    执行多尺度超小波变换，每个尺度使用不同的参数
    
    参数:
        data: 输入信号数据
        fs: 采样率
        scales: 尺度数量列表，对应于不同尺度
        base_cycles_list: 每个尺度的基准周期列表
        order_ranges: 每个尺度的超小波阶数范围列表，每个元素为元组(min_order, max_order)
        freq_ranges: 频率范围列表，每个元素为(min_freq, max_freq)；如果为None则自动分配
    
    返回:
        scale_results: 包含每个尺度时频图的列表
    """
    # 确保输入参数长度匹配
    n_scales = len(scales)
    if len(base_cycles_list) != n_scales or len(order_ranges) != n_scales:
        raise ValueError("scales, base_cycles_list和order_ranges参数长度必须相同")
    
    # 如果没有指定频率范围，则创建默认范围
    if freq_ranges is None:
        # 默认使用不同的频率范围，从低频到高频
        max_freq = fs / 2  # 最大频率为采样率的一半
        freq_ranges = []
        for i in range(n_scales):
            min_freq = 1 + (i * max_freq / (2 * n_scales))  # 不同尺度的最小频率
            max_freq_scale = max_freq - (i * max_freq / (3 * n_scales))  # 不同尺度的最大频率
            freq_ranges.append((min_freq, max_freq_scale))
    
    # 为每个尺度创建频率序列
    scale_results = []
    for i in range(n_scales):
        # 确定频率分bins数量（根据尺度确定）
        freq_bins = scales[i]
        freq_min, freq_max = freq_ranges[i]
        
        # 创建频率列表
        frequencies = np.linspace(freq_min, freq_max, freq_bins)
        
        # 获取当前尺度的参数
        base_cycles = base_cycles_list[i]
        orders = order_ranges[i]
        
        # 执行超小波变换
        scale_result = superlets(
            data=data,
            fs=fs,
            foi=frequencies,
            c1=base_cycles,
            ord=orders
        )
        
        scale_results.append(scale_result)
    
    return scale_results

def apply_pca_fusion(scale_results, n_components=1):
    """
    使用PCA对多尺度超小波变换结果进行融合
    
    参数:
        scale_results: 包含不同尺度超小波变换结果的列表
        n_components: 需要保留的主成分数量
    
    返回:
        fused_result: 融合后的结果
    """
    try:
        from sklearn.decomposition import PCA
    except ImportError:
        raise ImportError("该功能需要安装scikit-learn库: pip install scikit-learn")
    
    # 确保至少有一个尺度的结果
    if not scale_results or len(scale_results) == 0:
        raise ValueError("需要至少一个尺度的结果进行融合")
    
    # 获取每个尺度结果的形状
    n_scales = len(scale_results)
    
    # 提取维度信息
    if n_scales == 1:
        # 如果只有一个尺度，直接返回该结果
        return scale_results[0]
    
    # 获取形状信息
    freq_dim, time_dim = scale_results[0].shape
    
    # 将所有尺度的结果重塑为2D矩阵用于PCA
    # 每个时频点作为一个样本，每个尺度作为一个特征
    reshaped_data = np.zeros((freq_dim * time_dim, n_scales))
    
    for i in range(n_scales):
        # 确保当前尺度的结果可以被重塑
        if scale_results[i].shape != (freq_dim, time_dim):
            # 如果形状不同，需要进行插值调整
            from scipy.interpolate import interp2d
            
            orig_freq_dim, orig_time_dim = scale_results[i].shape
            x_orig = np.linspace(0, 1, orig_time_dim)
            y_orig = np.linspace(0, 1, orig_freq_dim)
            
            x_new = np.linspace(0, 1, time_dim)
            y_new = np.linspace(0, 1, freq_dim)
            
            interp_func = interp2d(x_orig, y_orig, scale_results[i], kind='linear')
            scale_results[i] = interp_func(x_new, y_new)
        
        # 将当前尺度的结果展平为一列
        reshaped_data[:, i] = scale_results[i].flatten()
    
    # 应用PCA进行降维
    pca = PCA(n_components=n_components)
    reduced_data = pca.fit_transform(reshaped_data)
    
    # 如果只提取一个主成分，则将结果重塑为原始形状
    if n_components == 1:
        fused_result = reduced_data.reshape(freq_dim, time_dim)
    else:
        # 如果提取多个主成分，返回第一个主成分重塑后的结果
        fused_result = reduced_data[:, 0].reshape(freq_dim, time_dim)
    
    return fused_result

def optimized_superlets(data, fs, ylim, multi_scale=True):
    """
    优化的超小波变换函数，可选择使用单尺度或多尺度变换
    
    参数:
        data: 输入信号数据
        fs: 采样率
        ylim: 频率上限
        multi_scale: 是否使用多尺度变换，默认为True
    
    返回:
        结果时频图
    """
    if not multi_scale:
        # 单尺度超小波变换
        frequencies = np.linspace(1, ylim, 100)
        result = superlets(
            data=data, 
            fs=fs, 
            foi=frequencies, 
            c1=5, 
            ord=(5, 5)
        )
        return result
    else:
        # 多尺度超小波变换
        # 定义三个不同的尺度，每个尺度使用不同的参数
        scales = [80, 100, 120]  # 每个尺度的频率bins数量
        
        # 对每个尺度使用不同的基准周期
        base_cycles_list = [3, 5, 8]
        
        # 对每个尺度使用不同的阶数范围
        order_ranges = [(3, 3), (5, 5), (8, 8)]
        
        # 对每个尺度使用不同的频率范围
        freq_min = 1
        freq_ranges = [
            (freq_min, ylim/2),        # 低频尺度
            (freq_min, ylim*3/4),      # 中频尺度
            (freq_min, ylim)           # 高频尺度
        ]
        
        # 执行多尺度超小波变换
        scale_results = multi_scale_superlets(
            data=data,
            fs=fs,
            scales=scales,
            base_cycles_list=base_cycles_list,
            order_ranges=order_ranges,
            freq_ranges=freq_ranges
        )
        
        # 使用PCA融合不同尺度的结果
        fused_result = apply_pca_fusion(scale_results, n_components=1)
        
        return fused_result
