# WTConvNeXt V2 Tiny Fine-tuning Configuration (小波卷积版本)

# Model settings
model_name: wtconvnextv2_tiny
model_dir: checkpoint/convnextv2_tiny/convnextv2_tiny_1k_224_ema.pt

# Dataset settings
train_dataset: A_SLT
train: train
vali: val
test: test
data_dir: ./datasets/img_dataset_split

# Training settings
batch_size: 16
n_epoch: 25
early_stop: 10

# Optimizer settings
lr: 0.0001
momentum: 0.9
weight_decay: 1e-5

# LR scheduler settings
lr_gamma: 0.0003
lr_decay: 0.75
lr_scheduler: true

num_workers: 16
# Confusion matrix
confmtx: 0

# t-SNE visualization
t_sne: 0 