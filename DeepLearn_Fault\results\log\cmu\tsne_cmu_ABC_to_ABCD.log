从配置文件加载预训练设置: pretrained=True
Namespace(config='configyaml/cmu_convnextv2_atto.yaml', seed=0, num_workers=16, model_name='cmu_convnextv2_atto', model_dir=None, pretrained=True, pretrained_path='checkpoint/convnextv2_atto/convnextv2_atto_1k_224_ema.pt', data_dir='./datasets/img_dataset_split', train_dataset='A_SLT,B_SLT,C_SLT', test_dataset='A_SLT,B_SLT,C_SLT,D_SLT', train='train', vali='val', test='test', is_training=0, batch_size=16, n_epoch=30, early_stop=30, lr=5e-05, momentum=0.9, weight_decay=1e-05, lr_gamma=0.0003, lr_decay=0.75, lr_scheduler=True, t_sne=1, confmtx=1, transfer_loss_weight=2.0, domain_adaptation_weight=1.0, entropy_weight=0.1, source_weight=2.0, target_weight=1.0, device=device(type='cuda'))
多源域训练: ./datasets/img_dataset_split/A_SLT/train,./datasets/img_dataset_split/B_SLT/train,./datasets/img_dataset_split/C_SLT/train
多源域验证: ./datasets/img_dataset_split/A_SLT/val,./datasets/img_dataset_split/B_SLT/val,./datasets/img_dataset_split/C_SLT/val
使用数据集 ./datasets/img_dataset_split/A_SLT/train 的类别映射: {0: '0', 1: '1', 2: '2', 3: '3', 4: '4', 5: '5', 6: '6', 7: '7', 8: '8', 9: '9'}
加载数据集 ./datasets/img_dataset_split/A_SLT/train: 1200 样本
加载数据集 ./datasets/img_dataset_split/B_SLT/train: 1200 样本
加载数据集 ./datasets/img_dataset_split/C_SLT/train: 1200 样本
合并后的数据集大小: 3600 样本
{0: '0', 1: '1', 2: '2', 3: '3', 4: '4', 5: '5', 6: '6', 7: '7', 8: '8', 9: '9'}
使用数据集 ./datasets/img_dataset_split/A_SLT/val 的类别映射: {0: '0', 1: '1', 2: '2', 3: '3', 4: '4', 5: '5', 6: '6', 7: '7', 8: '8', 9: '9'}
加载数据集 ./datasets/img_dataset_split/A_SLT/val: 400 样本
加载数据集 ./datasets/img_dataset_split/B_SLT/val: 400 样本
加载数据集 ./datasets/img_dataset_split/C_SLT/val: 400 样本
合并后的数据集大小: 1200 样本
{0: '0', 1: '1', 2: '2', 3: '3', 4: '4', 5: '5', 6: '6', 7: '7', 8: '8', 9: '9'}
多目标域训练: ./datasets/img_dataset_split/A_SLT/train,./datasets/img_dataset_split/B_SLT/train,./datasets/img_dataset_split/C_SLT/train,./datasets/img_dataset_split/D_SLT/train
多目标域测试: ./datasets/img_dataset_split/A_SLT/test,./datasets/img_dataset_split/B_SLT/test,./datasets/img_dataset_split/C_SLT/test,./datasets/img_dataset_split/D_SLT/test
使用数据集 ./datasets/img_dataset_split/A_SLT/train 的类别映射: {0: '0', 1: '1', 2: '2', 3: '3', 4: '4', 5: '5', 6: '6', 7: '7', 8: '8', 9: '9'}
加载数据集 ./datasets/img_dataset_split/A_SLT/train: 1200 样本
加载数据集 ./datasets/img_dataset_split/B_SLT/train: 1200 样本
加载数据集 ./datasets/img_dataset_split/C_SLT/train: 1200 样本
加载数据集 ./datasets/img_dataset_split/D_SLT/train: 1200 样本
合并后的数据集大小: 4800 样本
{0: '0', 1: '1', 2: '2', 3: '3', 4: '4', 5: '5', 6: '6', 7: '7', 8: '8', 9: '9'}
使用数据集 ./datasets/img_dataset_split/A_SLT/test 的类别映射: {0: '0', 1: '1', 2: '2', 3: '3', 4: '4', 5: '5', 6: '6', 7: '7', 8: '8', 9: '9'}
加载数据集 ./datasets/img_dataset_split/A_SLT/test: 400 样本
加载数据集 ./datasets/img_dataset_split/B_SLT/test: 400 样本
加载数据集 ./datasets/img_dataset_split/C_SLT/test: 400 样本
加载数据集 ./datasets/img_dataset_split/D_SLT/test: 400 样本
合并后的数据集大小: 1600 样本
{0: '0', 1: '1', 2: '2', 3: '3', 4: '4', 5: '5', 6: '6', 7: '7', 8: '8', 9: '9'}
创建模型前的预训练设置: pretrained=True, pretrained_path=checkpoint/convnextv2_atto/convnextv2_atto_1k_224_ema.pt
Model函数: 将使用指定的预训练模型路径: checkpoint/convnextv2_atto/convnextv2_atto_1k_224_ema.pt
Model函数: 创建CMU模型的特征提取器，预训练设置: pretrained=True, pretrained_path=checkpoint/convnextv2_atto/convnextv2_atto_1k_224_ema.pt
开始加载ConvNeXtV2 Atto预训练模型: checkpoint/convnextv2_atto/convnextv2_atto_1k_224_ema.pt
检测到预训练模型使用'model'键存储状态字典
移除预训练头部参数: head.bias
移除预训练头部参数: head.weight
将参数从 stages.0.0.dwconv.bias 转移到 stages.0.0.dwconv.base_conv.bias
将参数从 stages.0.0.dwconv.weight 转移到 stages.0.0.dwconv.base_conv.weight
将参数从 stages.0.1.dwconv.bias 转移到 stages.0.1.dwconv.base_conv.bias
将参数从 stages.0.1.dwconv.weight 转移到 stages.0.1.dwconv.base_conv.weight
将参数从 stages.1.0.dwconv.bias 转移到 stages.1.0.dwconv.base_conv.bias
将参数从 stages.1.0.dwconv.weight 转移到 stages.1.0.dwconv.base_conv.weight
将参数从 stages.1.1.dwconv.bias 转移到 stages.1.1.dwconv.base_conv.bias
将参数从 stages.1.1.dwconv.weight 转移到 stages.1.1.dwconv.base_conv.weight
将参数从 stages.2.0.dwconv.bias 转移到 stages.2.0.dwconv.base_conv.bias
将参数从 stages.2.0.dwconv.weight 转移到 stages.2.0.dwconv.base_conv.weight
将参数从 stages.2.1.dwconv.bias 转移到 stages.2.1.dwconv.base_conv.bias
将参数从 stages.2.1.dwconv.weight 转移到 stages.2.1.dwconv.base_conv.weight
将参数从 stages.2.2.dwconv.bias 转移到 stages.2.2.dwconv.base_conv.bias
将参数从 stages.2.2.dwconv.weight 转移到 stages.2.2.dwconv.base_conv.weight
将参数从 stages.2.3.dwconv.bias 转移到 stages.2.3.dwconv.base_conv.bias
将参数从 stages.2.3.dwconv.weight 转移到 stages.2.3.dwconv.base_conv.weight
将参数从 stages.2.4.dwconv.bias 转移到 stages.2.4.dwconv.base_conv.bias
将参数从 stages.2.4.dwconv.weight 转移到 stages.2.4.dwconv.base_conv.weight
将参数从 stages.2.5.dwconv.bias 转移到 stages.2.5.dwconv.base_conv.bias
将参数从 stages.2.5.dwconv.weight 转移到 stages.2.5.dwconv.base_conv.weight
将参数从 stages.3.0.dwconv.bias 转移到 stages.3.0.dwconv.base_conv.bias
将参数从 stages.3.0.dwconv.weight 转移到 stages.3.0.dwconv.base_conv.weight
将参数从 stages.3.1.dwconv.bias 转移到 stages.3.1.dwconv.base_conv.bias
将参数从 stages.3.1.dwconv.weight 转移到 stages.3.1.dwconv.base_conv.weight
成功加载ConvNeXtV2 Atto预训练模型: checkpoint/convnextv2_atto/convnextv2_atto_1k_224_ema.pt
加载了 138/138 层参数
成功创建CMU模型，特征提取器基于ConvNeXtV2 Atto，特征维度: 320
注意：特征提取器的预训练权重已通过ConvNeXtV2_atto函数加载
目标域测试准确率: 0.9988
t-SNE特征已保存到 results/t_sne_feature/cmu_convnextv2_atto__srcA_SLT,B_SLT,C_SLT__tgtA_SLT,B_SLT,C_SLT,D_SLT__bs16_seed0_tsne.json
